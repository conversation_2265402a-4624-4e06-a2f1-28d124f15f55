"use client";

import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { useQuery } from "@tanstack/react-query";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";
import { IFindingKnowledgeByIdDto } from "../../types/dtos/find-by-id-knowledge.dto";

export const useFindKnowledgeById = (id: string, enabled = true) => {
	const { canRead } = usePermissions();

	const {
		data,
		isLoading,
		isFetched,
		isError,
		error: queryError,
	} = useQuery({
		queryKey: chatKeys.detail(id.toString()),
		queryFn: () => createGetRequest<IFindingKnowledgeByIdDto>(CHAT_ENDPOINTS.FIND_KNOWLEDGE_BY_ID(id.toString())),
		enabled: canRead("all") && Boolean(id) && enabled,
	});

	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data : null,
		isLoading,
		isFetched,
		isError,
		hasError: !data?.success && isFetched && !isNoDataFound,
		error: !data?.success ? data?.data.message : queryError?.message,
		isEmpty: isNoDataFound,
	};
};
