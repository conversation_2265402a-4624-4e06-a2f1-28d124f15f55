import { THEME_PERSISTENCE_CONFIG } from "../constants/themes.constants";
import { IThemePersistence, IThemePersistenceConfig, THEME_CONSTANTS, ThemeId } from "../types/theme.types";

/**
 * Serviço de persistência de temas
 * Gerencia cookies para SSR e localStorage para client-side
 * Seguindo princípios SOLID e compatibilidade com Next.js 15
 */
export class ThemePersistenceService {
	private config: IThemePersistenceConfig;

	constructor(config: IThemePersistenceConfig = THEME_PERSISTENCE_CONFIG) {
		this.config = config;
	}

	/**
	 * Salva o tema nos cookies (client-side usando document.cookie)
	 */
	saveThemeToServer(themeId: ThemeId): void {
		if (typeof window === "undefined") return;

		try {
			const persistence: IThemePersistence = {
				theme: themeId,
				timestamp: Date.now(),
				version: this.config.version,
			};

			const cookieValue = JSON.stringify(persistence);
			const maxAge = this.config.maxAge;
			const secure = window.location.protocol === "https:";

			document.cookie = `${this.config.cookieName}=${encodeURIComponent(cookieValue)}; max-age=${maxAge}; path=/; samesite=lax${secure ? "; secure" : ""}`;
		} catch (error) {
			console.error("Erro ao salvar tema no servidor:", error);
		}
	}

	/**
	 * Carrega o tema dos cookies (client-side usando document.cookie)
	 */
	loadThemeFromServer(): ThemeId | null {
		if (typeof window === "undefined") return null;

		try {
			const cookies = document.cookie.split(";");
			const themeCookie = cookies.find(cookie => cookie.trim().startsWith(`${this.config.cookieName}=`));

			if (!themeCookie) {
				return null;
			}

			const cookieValue = decodeURIComponent(themeCookie.split("=")[1]);

			const persistence: IThemePersistence = JSON.parse(cookieValue);

			// Valida a versão
			if (persistence.version !== this.config.version) {
				console.warn("Versão de persistência incompatível, ignorando tema salvo");
				return null;
			}

			// Valida se não expirou
			const isExpired = Date.now() - persistence.timestamp > this.config.maxAge * 1000;
			if (isExpired) {
				console.warn("Tema persistido expirado");
				this.clearServerStorage();
				return null;
			}

			return persistence.theme;
		} catch (error) {
			console.error("Erro ao carregar tema do servidor:", error);
			return null;
		}
	}

	/**
	 * Salva o tema no localStorage (client-side)
	 */
	saveThemeToClient(themeId: ThemeId): void {
		if (typeof window === "undefined") return;

		try {
			const persistence: IThemePersistence = {
				theme: themeId,
				timestamp: Date.now(),
				version: this.config.version,
			};

			localStorage.setItem(this.config.localStorageKey, JSON.stringify(persistence));
		} catch (error) {
			console.error("Erro ao salvar tema no cliente:", error);
		}
	}

	/**
	 * Carrega o tema do localStorage (client-side)
	 */
	loadThemeFromClient(): ThemeId | null {
		if (typeof window === "undefined") return null;

		try {
			const stored = localStorage.getItem(this.config.localStorageKey);
			if (!stored) return null;

			const persistence: IThemePersistence = JSON.parse(stored);

			// Valida a versão
			if (persistence.version !== this.config.version) {
				console.warn("Versão de persistência incompatível, limpando localStorage");
				this.clearClientStorage();
				return null;
			}

			// Valida se não expirou
			const isExpired = Date.now() - persistence.timestamp > this.config.maxAge * 1000;
			if (isExpired) {
				console.warn("Tema persistido expirado, limpando localStorage");
				this.clearClientStorage();
				return null;
			}

			return persistence.theme;
		} catch (error) {
			console.error("Erro ao carregar tema do cliente:", error);
			this.clearClientStorage();
			return null;
		}
	}

	/**
	 * Sincroniza tema entre cookie e localStorage
	 */
	syncTheme(themeId: ThemeId): void {
		try {
			// Salva no servidor (cookie)
			this.saveThemeToServer(themeId);

			// Salva no cliente (localStorage)
			this.saveThemeToClient(themeId);
		} catch (error) {
			console.error("Erro ao sincronizar tema:", error);
			throw error;
		}
	}

	/**
	 * Obtém o tema com fallback inteligente
	 * Prioridade: localStorage > cookie > sistema > padrão
	 */
	getThemeWithFallback(): ThemeId {
		// 1. Tenta localStorage (mais rápido, client-side)
		const clientTheme = this.loadThemeFromClient();
		if (clientTheme) return clientTheme;

		// 2. Tenta cookie (client-side)
		const serverTheme = this.loadThemeFromServer();
		if (serverTheme) return serverTheme;

		// 3. Detecta preferência do sistema
		const systemTheme = this.getSystemPreference();
		if (systemTheme) return systemTheme;

		// 4. Fallback para tema padrão
		return THEME_CONSTANTS.DEFAULT_THEME;
	}

	/**
	 * Detecta preferência do sistema
	 */
	private getSystemPreference(): ThemeId | null {
		if (typeof window === "undefined") return null;

		try {
			const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
			return prefersDark ? "dark" : "light";
		} catch {
			return null;
		}
	}

	/**
	 * Limpa o armazenamento client-side
	 */
	clearClientStorage(): void {
		if (typeof window === "undefined") return;

		try {
			localStorage.removeItem(this.config.localStorageKey);
		} catch (error) {
			console.error("Erro ao limpar localStorage:", error);
		}
	}

	/**
	 * Limpa o armazenamento server-side
	 */
	clearServerStorage(): void {
		if (typeof window === "undefined") return;

		try {
			document.cookie = `${this.config.cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
		} catch (error) {
			console.error("Erro ao limpar cookie:", error);
		}
	}

	/**
	 * Limpa todo o armazenamento
	 */
	clearAllStorage(): void {
		this.clearServerStorage();
		this.clearClientStorage();
	}

	/**
	 * Migra dados de versões antigas
	 */
	migrateOldData(): void {
		// Implementar migração se necessário
		// Por exemplo, migrar de um formato antigo de cookie/localStorage
		try {
			// Verifica se existe dados no formato antigo
			const oldCookieValue =
				typeof window !== "undefined"
					? document.cookie
							.split(";")
							.find(c => c.trim().startsWith("theme="))
							?.split("=")[1]
					: null;

			if (oldCookieValue && oldCookieValue !== "undefined") {
				// Migra para o novo formato
				this.syncTheme(oldCookieValue as ThemeId);

				// Remove o cookie antigo
				if (typeof window !== "undefined") {
					document.cookie = "theme=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
				}
			}
		} catch (error) {
			console.warn("Erro na migração de dados antigos:", error);
		}
	}

	/**
	 * Valida se um tema é válido
	 */
	validateTheme(themeId: ThemeId): boolean {
		if (!themeId || typeof themeId !== "string") return false;

		// Lista de temas válidos (pode ser expandida)
		const validThemes = ["light", "dark", "system"];
		const isStandardTheme = validThemes.includes(themeId);
		const isCustomTheme = themeId.startsWith("custom-");

		return isStandardTheme || isCustomTheme;
	}

	/**
	 * Obtém estatísticas de uso
	 */
	getUsageStats(): { lastUsed: number | null; version: string } {
		if (typeof window === "undefined") {
			return { lastUsed: null, version: this.config.version };
		}

		try {
			const stored = localStorage.getItem(this.config.localStorageKey);
			if (!stored) return { lastUsed: null, version: this.config.version };

			const persistence: IThemePersistence = JSON.parse(stored);
			return {
				lastUsed: persistence.timestamp,
				version: persistence.version,
			};
		} catch {
			return { lastUsed: null, version: this.config.version };
		}
	}
}

// Instância singleton do serviço
export const themePersistenceService = new ThemePersistenceService();
