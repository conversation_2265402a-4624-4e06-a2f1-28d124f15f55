import { cookies } from "next/headers";
import { THEME_PERSISTENCE_CONFIG } from "../constants/themes.constants";
import { IThemePersistence, IThemePersistenceConfig, THEME_CONSTANTS, ThemeId } from "../types/theme.types";

/**
 * Serviço de persistência de temas
 * Gerencia cookies para SSR e localStorage para client-side
 * Seguindo princípios SOLID e compatibilidade com Next.js 15
 */
export class ThemePersistenceService {
	private config: IThemePersistenceConfig;

	constructor(config: IThemePersistenceConfig = THEME_PERSISTENCE_CONFIG) {
		this.config = config;
	}

	/**
	 * Salva o tema nos cookies (server-side)
	 */
	async saveThemeToServer(themeId: ThemeId): Promise<void> {
		try {
			const cookieStore = await cookies();
			const persistence: IThemePersistence = {
				theme: themeId,
				timestamp: Date.now(),
				version: this.config.version,
			};

			cookieStore.set(this.config.cookieName, JSON.stringify(persistence), {
				maxAge: this.config.maxAge,
				httpOnly: false, // Permite acesso client-side
				secure: process.env.NODE_ENV === "production",
				sameSite: "lax",
				path: "/",
			});
		} catch (error) {
			console.error("Erro ao salvar tema no servidor:", error);
			throw new Error("Falha ao persistir tema no servidor");
		}
	}

	/**
	 * Carrega o tema dos cookies (server-side)
	 */
	async loadThemeFromServer(): Promise<ThemeId | null> {
		try {
			const cookieStore = await cookies();
			const cookieValue = cookieStore.get(this.config.cookieName)?.value;

			if (!cookieValue) {
				return null;
			}

			const persistence: IThemePersistence = JSON.parse(cookieValue);

			// Valida a versão
			if (persistence.version !== this.config.version) {
				console.warn("Versão de persistência incompatível, ignorando tema salvo");
				return null;
			}

			// Valida se não expirou (opcional, já que o cookie tem maxAge)
			const isExpired = Date.now() - persistence.timestamp > this.config.maxAge * 1000;
			if (isExpired) {
				console.warn("Tema persistido expirado");
				return null;
			}

			return persistence.theme;
		} catch (error) {
			console.error("Erro ao carregar tema do servidor:", error);
			return null;
		}
	}

	/**
	 * Salva o tema no localStorage (client-side)
	 */
	saveThemeToClient(themeId: ThemeId): void {
		if (typeof window === "undefined") return;

		try {
			const persistence: IThemePersistence = {
				theme: themeId,
				timestamp: Date.now(),
				version: this.config.version,
			};

			localStorage.setItem(this.config.localStorageKey, JSON.stringify(persistence));
		} catch (error) {
			console.error("Erro ao salvar tema no cliente:", error);
		}
	}

	/**
	 * Carrega o tema do localStorage (client-side)
	 */
	loadThemeFromClient(): ThemeId | null {
		if (typeof window === "undefined") return null;

		try {
			const stored = localStorage.getItem(this.config.localStorageKey);
			if (!stored) return null;

			const persistence: IThemePersistence = JSON.parse(stored);

			// Valida a versão
			if (persistence.version !== this.config.version) {
				console.warn("Versão de persistência incompatível, limpando localStorage");
				this.clearClientStorage();
				return null;
			}

			// Valida se não expirou
			const isExpired = Date.now() - persistence.timestamp > this.config.maxAge * 1000;
			if (isExpired) {
				console.warn("Tema persistido expirado, limpando localStorage");
				this.clearClientStorage();
				return null;
			}

			return persistence.theme;
		} catch (error) {
			console.error("Erro ao carregar tema do cliente:", error);
			this.clearClientStorage();
			return null;
		}
	}

	/**
	 * Sincroniza tema entre cookie e localStorage
	 */
	async syncTheme(themeId: ThemeId): Promise<void> {
		try {
			// Salva no servidor (cookie)
			await this.saveThemeToServer(themeId);

			// Salva no cliente (localStorage)
			this.saveThemeToClient(themeId);
		} catch (error) {
			console.error("Erro ao sincronizar tema:", error);
			throw error;
		}
	}

	/**
	 * Obtém o tema com fallback inteligente
	 * Prioridade: localStorage > cookie > sistema > padrão
	 */
	async getThemeWithFallback(): Promise<ThemeId> {
		// 1. Tenta localStorage (mais rápido, client-side)
		const clientTheme = this.loadThemeFromClient();
		if (clientTheme) return clientTheme;

		// 2. Tenta cookie (SSR)
		const serverTheme = await this.loadThemeFromServer();
		if (serverTheme) return serverTheme;

		// 3. Detecta preferência do sistema
		const systemTheme = this.getSystemPreference();
		if (systemTheme) return systemTheme;

		// 4. Fallback para tema padrão
		return THEME_CONSTANTS.DEFAULT_THEME;
	}

	/**
	 * Detecta preferência do sistema
	 */
	private getSystemPreference(): ThemeId | null {
		if (typeof window === "undefined") return null;

		try {
			const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
			return prefersDark ? "dark" : "light";
		} catch {
			return null;
		}
	}

	/**
	 * Limpa o armazenamento client-side
	 */
	clearClientStorage(): void {
		if (typeof window === "undefined") return;

		try {
			localStorage.removeItem(this.config.localStorageKey);
		} catch (error) {
			console.error("Erro ao limpar localStorage:", error);
		}
	}

	/**
	 * Limpa o armazenamento server-side
	 */
	async clearServerStorage(): Promise<void> {
		try {
			const cookieStore = await cookies();
			cookieStore.delete(this.config.cookieName);
		} catch (error) {
			console.error("Erro ao limpar cookie:", error);
		}
	}

	/**
	 * Limpa todo o armazenamento
	 */
	async clearAllStorage(): Promise<void> {
		await this.clearServerStorage();
		this.clearClientStorage();
	}

	/**
	 * Migra dados de versões antigas
	 */
	async migrateOldData(): Promise<void> {
		// Implementar migração se necessário
		// Por exemplo, migrar de um formato antigo de cookie/localStorage
		try {
			// Verifica se existe dados no formato antigo
			const oldCookieValue =
				typeof window !== "undefined"
					? document.cookie
							.split(";")
							.find(c => c.trim().startsWith("theme="))
							?.split("=")[1]
					: null;

			if (oldCookieValue && oldCookieValue !== "undefined") {
				// Migra para o novo formato
				await this.syncTheme(oldCookieValue as ThemeId);

				// Remove o cookie antigo
				if (typeof window !== "undefined") {
					document.cookie = "theme=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
				}
			}
		} catch (error) {
			console.warn("Erro na migração de dados antigos:", error);
		}
	}

	/**
	 * Valida se um tema é válido
	 */
	validateTheme(themeId: ThemeId): boolean {
		if (!themeId || typeof themeId !== "string") return false;

		// Lista de temas válidos (pode ser expandida)
		const validThemes = ["light", "dark", "system"];
		const isStandardTheme = validThemes.includes(themeId);
		const isCustomTheme = themeId.startsWith("custom-");

		return isStandardTheme || isCustomTheme;
	}

	/**
	 * Obtém estatísticas de uso
	 */
	getUsageStats(): { lastUsed: number | null; version: string } {
		if (typeof window === "undefined") {
			return { lastUsed: null, version: this.config.version };
		}

		try {
			const stored = localStorage.getItem(this.config.localStorageKey);
			if (!stored) return { lastUsed: null, version: this.config.version };

			const persistence: IThemePersistence = JSON.parse(stored);
			return {
				lastUsed: persistence.timestamp,
				version: persistence.version,
			};
		} catch {
			return { lastUsed: null, version: this.config.version };
		}
	}
}

// Instância singleton do serviço
export const themePersistenceService = new ThemePersistenceService();
