import { getAllCookies } from "@/shared/lib/cookies/crud/get";
import { cookies } from "next/headers";

jest.mock("next/headers", () => ({
	cookies: jest.fn(),
}));

const mockGetAll = jest.fn();

beforeEach(() => {
	jest.clearAllMocks();
	(cookies as jest.Mock).mockResolvedValue({
		getAll: mockGetAll,
	});
});

describe("getAllCookies", () => {
	it("deve retornar todos os cookies", async () => {
		mockGetAll.mockReturnValueOnce([{ name: "testCookie", value: "cookieValue" }]);
		const { success, value } = await getAllCookies();
		expect(cookies).toHaveBeenCalled();
		expect(mockGetAll).toHaveBeenCalled();
		expect(success).toBe(true);
		expect(value).toEqual({ testCookie: "cookieValue" });
	});

	it("deve falhar ao buscar todos os cookies quando nenhum cookie for encontrado", async () => {
		mockGetAll.mockReturnValueOnce([]);
		const { success, message, value } = await getAllCookies();
		expect(cookies).toHaveBeenCalled();
		expect(mockGetAll).toHaveBeenCalled();
		expect(success).toBe(false);
		expect(value).toBeNull();
		expect(message).toMatch(/Nenhum cookie foi encontrado|Erro ao buscar todos os cookies/i);
	});
});
