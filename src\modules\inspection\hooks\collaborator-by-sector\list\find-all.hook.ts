"use client";
import { COLLAB_BY_SECTOR_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { ICollabBySectorParamsDto, IInspectionCollabBySector } from "@/modules/inspection/types/collaborator-by-sector/find-all.dto";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export const useFindAllCollabBySector = ({ page = 1, limit = 10, search = "" }: ICollabBySectorParamsDto) => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.collabBysector.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IInspectionCollabBySector>>(COLLAB_BY_SECTOR_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR),
	});

	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
