import { cn } from "@/shared/lib/shadcn/utils";
import { Bo<PERSON> } from "lucide-react";

interface TypingIndicatorProps {
	className?: string;
}

export const TypingIndicator = ({ className }: TypingIndicatorProps) => {
	return (
		<div className={cn("group flex gap-2.5 px-3 py-2 transition-all duration-200 hover:bg-slate-50/50 dark:hover:bg-slate-800/30", className)}>
			<div className="flex h-7 w-7 shrink-0 items-center justify-center rounded-full border border-blue-200/60 bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 shadow-sm transition-all duration-200 group-hover:scale-105 group-hover:shadow-md dark:border-blue-800/60 dark:from-blue-950/60 dark:to-blue-900/60 dark:text-blue-400">
				<Bot className="h-3.5 w-3.5" />
			</div>
			<div className="flex max-w-[75%] flex-col gap-1">
				<div className="rounded-2xl border border-slate-200/60 bg-gradient-to-br from-slate-50/90 to-white/90 px-3 py-2.5 text-slate-700 shadow-sm shadow-slate-200/40 backdrop-blur-sm transition-all duration-200 group-hover:shadow-md dark:border-slate-600/60 dark:from-slate-800/90 dark:to-slate-700/90 dark:text-slate-200 dark:shadow-slate-800/40">
					<div className="flex items-center gap-2">
						<div className="flex gap-1">
							<div className="h-1.5 w-1.5 animate-bounce rounded-full bg-slate-400/80 [animation-delay:-0.3s] dark:bg-slate-500/80" />
							<div className="h-1.5 w-1.5 animate-bounce rounded-full bg-slate-400/80 [animation-delay:-0.15s] dark:bg-slate-500/80" />
							<div className="h-1.5 w-1.5 animate-bounce rounded-full bg-slate-400/80 dark:bg-slate-500/80" />
						</div>
						<span className="text-xs font-medium text-slate-500 dark:text-slate-400">Doorinha está pensando...</span>
					</div>
				</div>
			</div>
		</div>
	);
};
