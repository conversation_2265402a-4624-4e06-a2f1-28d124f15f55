"use client";

import { useEffect, useState } from "react";
import { getAuthToken } from "../../lib/auth-actions";

interface IUseAuthCheckReturn {
	hasToken: boolean | null;
	isChecking: boolean;
	checkAuth: () => Promise<void>;
}

export function useAuthCheck(): IUseAuthCheckReturn {
	const [hasToken, setHasToken] = useState<boolean | null>(null);
	const [isChecking, setIsChecking] = useState(true);

	const checkAuth = async () => {
		setIsChecking(true);
		try {
			const token = await getAuthToken();
			setHasToken(!!token);
		} catch (error) {
			console.error("Erro ao verificar token:", error);
			setHasToken(false);
		} finally {
			setIsChecking(false);
		}
	};

	useEffect(() => {
		checkAuth();
	}, []);

	return {
		hasToken,
		isChecking,
		checkAuth,
	};
}
