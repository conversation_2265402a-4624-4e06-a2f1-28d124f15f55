"use client";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { COLLABORATOR_ENDPOINTS } from "../../api/endpoints";
import { collaboratorQueryKeys } from "../../constants/query";
import { COLLABORATOR_SUBJECTS } from "../../constants/subjects";
import { ICollaboratorDto } from "../../types/find-all.dto";

interface IFindAllCollaboratorParams {
	page?: number;
	limit?: number;
	search?: string;
}

export const useFindAllCollaborator = ({ page, limit, search }: IFindAllCollaboratorParams) => {
	const { canRead } = usePermissions();
	const { data, isLoading, isFetched } = useQuery({
		queryKey: collaboratorQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<ICollaboratorDto>>(COLLABORATOR_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(COLLABORATOR_SUBJECTS.COLLABORATOR),
	});

	const isNoDataFound = !data?.success && data?.status === 404;
	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
