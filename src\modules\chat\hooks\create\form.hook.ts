"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { addKnowledgeSchema, TAddKnowledgeSchema } from "../../validators/add-knowledge";

export const useAddKnowledgeForm = () => {
	return useForm<TAddKnowledgeSchema>({
		resolver: zodResolver(addKnowledgeSchema),
		defaultValues: {
			title: "",
			content: "",
		},
		mode: "onChange",
	});
};
