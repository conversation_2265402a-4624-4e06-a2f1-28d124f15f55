import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import { useFindKnowledgeById } from "../../hooks/list/find-by-id.hook";
import { IFindingKnowledgeByIdDto } from "../../types/dtos/find-by-id-knowledge.dto";

if (!globalThis.fetch) globalThis.fetch = jest.fn();

jest.mock("@/shared/lib/requests", () => ({
	createGetRequest: jest.fn(),
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canRead: () => true })),
}));

const mockedCreateGetRequest = createGetRequest as jest.MockedFunction<typeof createGetRequest>;

const mockedFormById: IFindingKnowledgeByIdDto = {
	id: 1,
	content: "Conteúdo do conhecimento",
	createdAt: new Date(),
	isActive: true,
	title: "Título do conhecimento",
};

const mockSuccessResponse: ApiResponse<IFindingKnowledgeByIdDto> = {
	data: mockedFormById,
	success: true,
	status: 200,
};

const mockEmptyResponse: ApiResponse<IFindingKnowledgeByIdDto> = {
	data: { message: "Conhecimento não encontrado" },
	success: false,
	status: 404,
};

const mockErrorResponse: ApiResponse<IFindingKnowledgeByIdDto> = {
	data: { message: "Erro ao buscar o conhecimento" },
	success: false,
	status: 500,
};

describe("useFindKnowledgeById", () => {
	let queryClient: QueryClient;

	beforeEach(() => {
		queryClient = new QueryClient({
			defaultOptions: {
				queries: {
					retry: false,
				},
			},
		});
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve buscar o conhecimento por ID com sucesso", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);
		const { result } = renderHook(() => useFindKnowledgeById("1"), { wrapper });
		await waitFor(() => expect(result.current.data).toEqual(mockedFormById));
		expect(result.current.hasError).toBe(false);
	});

	it("deve lidar com conhecimento não encontrado (404)", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockEmptyResponse);
		const { result } = renderHook(() => useFindKnowledgeById("1"), { wrapper });
		await waitFor(() => expect(result.current.isEmpty).toBe(true));
		expect(result.current.data).toEqual(null);
		expect(result.current.hasError).toBe(false);
		expect(result.current.error).toBe("Conhecimento não encontrado");
	});

	it("deve lidar com erro ao buscar o conhecimento (500)", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockErrorResponse);
		const { result } = renderHook(() => useFindKnowledgeById("1"), { wrapper });
		await waitFor(() => expect(result.current.hasError).toBe(true));
		expect(result.current.data).toEqual(null);
		expect(result.current.error).toBe("Erro ao buscar o conhecimento");
	});
});
