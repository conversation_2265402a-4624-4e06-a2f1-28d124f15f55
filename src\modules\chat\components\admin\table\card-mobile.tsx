import { IChatKnowledgeDto } from "@/modules/chat/types/dtos/find-all-knowledge.dto";
import { ProtectedComponent } from "@/shared/components/auth/protected";
import { Badge } from "@/shared/components/shadcn/badge";
import { Button } from "@/shared/components/shadcn/button";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { CheckCircle, Clock, Pencil, Trash, XCircle } from "lucide-react";
import { DeleteKnowledgeModal } from "../delete/delete-knowledge-modal";
import { EditKnowledgeModal } from "../edit/edit-knowledge-modal";

interface IChatCardMobileProps {
	item: IChatKnowledgeDto;
}

export const ChatCardMobile = ({ item }: IChatCardMobileProps) => {
	const modals = {
		edit: useModal(),
		delete: useModal(),
	};

	return (
		<Card className="relative border bg-white shadow-sm transition-shadow hover:shadow-md">
			<CardContent>
				<div className="mb-3 flex items-start justify-between">
					<h3 className="mr-2 line-clamp-2 flex-1 text-base leading-tight font-semibold text-gray-900">{item.title}</h3>
					<div className="flex items-center gap-2">
						<Badge variant={item.isActive ? "default" : "secondary"} className="flex items-center gap-1 text-xs">
							{item.isActive ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
							{item.isActive ? "Ativo" : "Inativo"}
						</Badge>
					</div>
				</div>

				<div className="mb-4 space-y-2">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-1 text-gray-500">
							<Clock className="h-3 w-3" />
							<span className="text-xs">Criado em</span>
						</div>
						<span className="text-xs font-medium text-gray-700">
							{new Date(item.createdAt).toLocaleDateString("pt-BR", {
								day: "2-digit",
								month: "2-digit",
								year: "numeric",
								hour: "2-digit",
								minute: "2-digit",
							})}
						</span>
					</div>
				</div>

				<Separator className="my-4" />

				<div className="flex gap-2">
					<ProtectedComponent action="manage" subject="all">
						<Button
							size="sm"
							variant="ghost"
							className="bg-primary/10 text-primary hover:bg-primary/20 border-primary h-8 flex-1 border px-2 text-xs"
							onClick={modals.edit.toggleModal}
						>
							<Pencil className="h-3 w-3" />
							<span>Editar</span>
						</Button>

						<Button
							size="sm"
							variant="ghost"
							className="h-8 flex-1 border border-red-400 bg-red-400/10 px-2 text-xs text-red-400 hover:bg-red-50 hover:text-red-700"
							onClick={modals.delete.toggleModal}
						>
							<Trash className="h-3 w-3" />
							<span>Excluir</span>
						</Button>
					</ProtectedComponent>
				</div>
			</CardContent>

			<EditKnowledgeModal isOpen={modals.edit.isOpen} onClose={modals.edit.closeModal} id={item.id.toString()} />
			<DeleteKnowledgeModal isOpen={modals.delete.isOpen} onClose={modals.delete.closeModal} title={item.title} id={item.id.toString()} />
		</Card>
	);
};
