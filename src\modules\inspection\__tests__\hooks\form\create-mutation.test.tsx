import { useCreateFormMutation } from "@/modules/inspection/hooks/form/create/mutation.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";

jest.mock("@/shared/lib/requests", () => ({
	createPostRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canCreate: () => true })),
}));

const mockedCreatePostRequest = createPostRequest as jest.MockedFunction<typeof createPostRequest>;
const mockSuccess: IMessageGlobalReturn = { message: "Formulário criado com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Error ao criar o formulário" };

describe("useCreateForm", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve criar o formulário com sucesso", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useCreateFormMutation(), { wrapper });
		await expect(
			result.current.createForm({
				approverId: "1",
				developerId: "2",
				nomenclature: "HRL",
				fields: [],
				title: "Novo Formulário",
				text: "Descrição do novo formulário",
			}),
		).resolves.toEqual(mockSuccess);
	});

	it("deve lançar erro ao falhar", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useCreateFormMutation(), { wrapper });
		await expect(
			result.current.createForm({
				approverId: "1",
				developerId: "2",
				nomenclature: "HRL",
				fields: [],
				title: "Novo Formulário",
				text: "Descrição do novo formulário",
			}),
		).rejects.toThrow("Error ao criar o formulário");
	});
});
