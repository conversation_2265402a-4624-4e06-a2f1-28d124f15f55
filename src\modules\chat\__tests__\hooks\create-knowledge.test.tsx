import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";
import { useAddKnowledgeMutation } from "../../hooks/create/mutation.hook";

jest.mock("@/shared/lib/requests", () => ({
	createPostRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canCreate: () => true })),
}));

const mockedCreatePostRequest = createPostRequest as jest.MockedFunction<typeof createPostRequest>;
const mockSuccess: IMessageGlobalReturn = { message: "Conhecimento adicionado com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Error ao adicionar o conhecimento" };

describe("useAddKnowledgeMutation", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve adicionar o conhecimento com sucesso", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useAddKnowledgeMutation(() => {}), { wrapper });
		await expect(
			result.current.addKnowledge({
				title: "Novo Conhecimento",
				content: "Conteúdo do conhecimento",
			}),
		).resolves.toEqual(mockSuccess);
	});

	it("deve lançar erro ao falhar", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useAddKnowledgeMutation(() => {}), { wrapper });
		await expect(result.current.addKnowledge({ title: "Novo Conhecimento", content: "Conteúdo do conhecimento" })).rejects.toThrow(
			"Error ao adicionar o conhecimento",
		);
	});
});
