"use client";

import { pathService } from "@/config/path-manager/service";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Plus, Search } from "lucide-react";
import { ComponentType, useState } from "react";
import { AddKnowledgeModal } from "../components/admin/create/add-knowledge-modal";
import { TableChatAdmin } from "../components/admin/table/table";

export const ChatAdminScreen = () => {
	const [searchTerm, setSearchTerm] = useState("");
	const item = pathService.getItemById("chat");
	const Icon = item?.icon as ComponentType<unknown> | undefined;
	const addKnowledgeModal = useModal();

	return (
		<main id="chat-admin" className="flex h-full w-full flex-1 flex-col gap-6">
			<header className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
				<div className="flex min-w-0 items-center gap-4">
					<div className="border-primary bg-muted text-primary flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg border">
						{Icon ? <Icon /> : null}
					</div>
					<div className="min-w-0">
						<h1 className="truncate text-2xl font-semibold text-gray-900">Chat Admin</h1>
						<p className="mt-1 truncate text-sm text-gray-600">Gerencie o conhecimento do chatbot de IA</p>
					</div>
				</div>
				<div className="flex w-full flex-col-reverse gap-2 sm:w-auto sm:flex-row sm:items-center sm:gap-3">
					<div className="relative w-full sm:min-w-0 sm:flex-1">
						<Search className="text-muted-foreground absolute top-1/2 left-3 size-4 -translate-y-1/2" />
						<Input
							aria-label="Pesquisar conhecimentos"
							placeholder="Pesquisar..."
							value={searchTerm}
							onChange={e => setSearchTerm(e.target.value)}
							className="focus:border-primary/50 h-10 w-full border-2 border-gray-200 bg-white pl-10 transition-all duration-200"
						/>
					</div>

					<Button onClick={() => addKnowledgeModal.toggleModal()} className="flex h-10 w-full items-center justify-center gap-2 sm:w-auto">
						<Plus className="h-4 w-4" />
						<span className="whitespace-nowrap">Adicionar Conhecimento</span>
					</Button>
				</div>
			</header>
			<div className="flex-1 overflow-auto">
				<TableChatAdmin searchTerm={searchTerm} />
			</div>
			<AddKnowledgeModal isOpen={addKnowledgeModal.isOpen} onClose={addKnowledgeModal.toggleModal} />
		</main>
	);
};
