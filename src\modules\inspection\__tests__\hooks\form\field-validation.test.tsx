import { getFieldValidationErrorsAtom, isFieldValidAtom } from "@/modules/inspection/atoms/forms/fields/field-validation.atom";
import { useFieldValidation, useFieldValidationStyles } from "@/modules/inspection/hooks/form/error-fields-wrapper/use-field-validation.hook";
import { renderHook } from "@testing-library/react";

jest.mock("jotai", () => {
	const actual = jest.requireActual("jotai");
	return {
		...actual,
		useAtomValue: jest.fn(),
	};
});

import { useAtomValue } from "jotai";

describe("useFieldValidation", () => {
	const tempId = "temp-1";

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("deve retornar estado sem erros quando não houver mensagens e não for válido", () => {
		(useAtomValue as jest.Mock).mockImplementation((atom: unknown) => {
			if (atom === getFieldValidationErrorsAtom) return () => ({});
			if (atom === isFieldValidAtom) return () => false;
			return undefined;
		});

		const { result } = renderHook(() => useFieldValidation(tempId));

		expect(result.current.hasErrors).toBe(false);
		expect(result.current.errors).toEqual({});
		expect(result.current.isValid).toBe(false);
		expect(result.current.errorMessages).toEqual([]);
		expect(result.current.firstError).toBeUndefined();
	});

	it("deve retornar estado válido quando isFieldValid for true", () => {
		(useAtomValue as jest.Mock).mockImplementation((atom: unknown) => {
			if (atom === getFieldValidationErrorsAtom) return () => ({});
			if (atom === isFieldValidAtom) return () => true;
			return undefined;
		});

		const { result } = renderHook(() => useFieldValidation(tempId));

		expect(result.current.hasErrors).toBe(false);
		expect(result.current.isValid).toBe(true);
		expect(result.current.errorMessages).toEqual([]);
	});

	it("deve retornar erros quando houver mensagens de validação", () => {
		const errors = { required: ["Campo obrigatório"], pattern: ["Formato inválido"] };

		(useAtomValue as jest.Mock).mockImplementation((atom: unknown) => {
			if (atom === getFieldValidationErrorsAtom) return () => errors;
			if (atom === isFieldValidAtom) return () => false;
			return undefined;
		});
		const { result } = renderHook(() => useFieldValidation(tempId));
		expect(result.current.hasErrors).toBe(true);
		expect(result.current.errors).toEqual(errors);
		expect(result.current.isValid).toBe(false);
		expect(result.current.errorMessages).toEqual(["Campo obrigatório", "Formato inválido"]);
		expect(result.current.firstError).toBe("Campo obrigatório");
	});

	describe("useFieldValidationStyles", () => {
		it("deve aplicar classes neutras quando sem erros e não válido", () => {
			(useAtomValue as jest.Mock).mockImplementation((atom: unknown) => {
				if (atom === getFieldValidationErrorsAtom) return () => ({});
				if (atom === isFieldValidAtom) return () => false;
				return undefined;
			});

			const { result } = renderHook(() => useFieldValidationStyles(tempId));

			expect(result.current.container).toContain("transition-colors");
			expect(result.current.input).toBe("");
			expect(result.current.label).toBe("");
			expect(result.current.error).toBe("");
		});

		it("deve aplicar classes de sucesso quando válido", () => {
			(useAtomValue as jest.Mock).mockImplementation((atom: unknown) => {
				if (atom === getFieldValidationErrorsAtom) return () => ({});
				if (atom === isFieldValidAtom) return () => true;
				return undefined;
			});

			const { result } = renderHook(() => useFieldValidationStyles(tempId));

			expect(result.current.container).toContain("border-green-200");
			expect(result.current.input).toContain("border-green-300");
			expect(result.current.label).toContain("text-green-700");
		});

		it("deve aplicar classes de erro quando houver erros", () => {
			const errors = { required: ["Campo obrigatório"] };
			(useAtomValue as jest.Mock).mockImplementation((atom: unknown) => {
				if (atom === getFieldValidationErrorsAtom) return () => errors;
				if (atom === isFieldValidAtom) return () => false;
				return undefined;
			});

			const { result } = renderHook(() => useFieldValidationStyles(tempId));

			expect(result.current.container).toContain("border-red-200");
			expect(result.current.input).toContain("border-red-300");
			expect(result.current.label).toContain("text-red-700");
			expect(result.current.error).toContain("text-red-600");
		});
	});
});
