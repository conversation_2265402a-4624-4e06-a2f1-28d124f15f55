"use client";
import { toast } from "@/core/toast";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";
import { TAddKnowledgeSchema } from "../../validators/add-knowledge";

export const useAddKnowledgeMutation = (onClose: () => void) => {
	const queryClient = useQueryClient();
	const { canCreate } = usePermissions();

	const mutation = useMutation<IMessageGlobalReturn, Error, TAddKnowledgeSchema>({
		mutationKey: chatKeys.custom("add-knowledge"),
		mutationFn: async form => {
			if (!canCreate("all")) throw new Error("Você não tem permissão para adicionar conhecimento. Apenas administradores podem realizar esta ação.");
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(CHAT_ENDPOINTS.ADD_KNOWLEDGE, form);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => {
			chatKeys.invalidateAll(queryClient);
			onClose();
		},
	});

	return {
		addKnowledge: (form: TAddKnowledgeSchema) =>
			toast.promise(mutation.mutateAsync(form), {
				loading: "Adicionando conhecimento...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
		...mutation,
	};
};
