import { IDefineCaslPermission, ROLE_LIST, TRoleList } from "@/config/permissions";
import { ACTION_LIST, TPermissionAction } from "@/config/permissions/actions";
import { SUBJECT_LIST, TPermissionSubject } from "@/config/permissions/subjects";
import { IUser } from "@/core/auth/types/user.types";
import { decodeJWT } from "@/shared/lib/jwt/decode";
import { IDecodedAccessToken } from "@/shared/types/cookies/decoded-access-token";
import { NextRequest } from "next/server";

export interface ITokenValidationResult {
	isValid: boolean;
	user?: IUser;
	error?: string;
}

export interface ITokenValidationService {
	validateToken(request: NextRequest): Promise<ITokenValidationResult>;
	extractUserFromToken(decodedToken: IDecodedAccessToken): IUser;
}

export class TokenValidationService implements ITokenValidationService {
	private static instance: TokenValidationService;
	private tokenCache = new Map<string, { user: IUser; expiresAt: number }>();
	private readonly CACHE_TTL = 5 * 60 * 1000;

	public static getInstance(): TokenValidationService {
		if (!TokenValidationService.instance) TokenValidationService.instance = new TokenValidationService();
		return TokenValidationService.instance;
	}

	public async validateToken(request: NextRequest): Promise<ITokenValidationResult> {
		try {
			const { pathname } = request.nextUrl;
			if (this.isPublicRoute(pathname)) return { isValid: true };

			const token = this.getTokenFromRequest(request);
			if (!token) {
				return {
					isValid: false,
					error: "Token de acesso não encontrado",
				};
			}

			const cachedUser = this.getCachedUserIfValid(token, pathname);
			if (cachedUser) return { isValid: true, user: cachedUser };

			const decodedToken = this.decodeAndValidateToken(token);
			if (!decodedToken) {
				return {
					isValid: false,
					error: "Token inválido ou expirado",
				};
			}

			const user = this.extractUserFromToken(decodedToken);
			this.cacheUser(token, pathname, user);

			return { isValid: true, user };
		} catch (error) {
			console.error("Erro na validação do token:", error);
			return {
				isValid: false,
				error: "Erro interno na validação",
			};
		}
	}

	private getTokenFromRequest(request: NextRequest): string | undefined {
		return request.cookies.get("access_token")?.value;
	}

	private getCachedUserIfValid(token: string, pathname: string): IUser | undefined {
		const cacheKey = `${token}_${pathname}`;
		const cached = this.tokenCache.get(cacheKey);
		if (cached && Date.now() < cached.expiresAt) return cached.user;
		return undefined;
	}

	private decodeAndValidateToken(token: string): IDecodedAccessToken | undefined {
		const decoded = decodeJWT<IDecodedAccessToken>(token);
		if (!decoded) return undefined;
		if (this.isTokenExpired(decoded)) return undefined;
		return decoded;
	}

	private cacheUser(token: string, pathname: string, user: IUser): void {
		const cacheKey = `${token}_${pathname}`;
		this.tokenCache.set(cacheKey, {
			user,
			expiresAt: Date.now() + this.CACHE_TTL,
		});
	}

	public extractUserFromToken(decodedToken: IDecodedAccessToken): IUser {
		return {
			id: decodedToken.sub ? String(decodedToken.sub) : "",
			name: decodedToken.name ? String(decodedToken.name) : "",
			email: decodedToken.email ? String(decodedToken.email) : "",
			roles: this.extractRoles(decodedToken),
			permissions: this.extractPermissions(decodedToken),
		};
	}

	private isPublicRoute(pathname: string): boolean {
		return pathname.startsWith("/auth/") || pathname.startsWith("/api/") || pathname.includes(".") || pathname === "/favicon.ico";
	}

	private isTokenExpired(decodedToken: IDecodedAccessToken): boolean {
		const currentTime = Math.floor(Date.now() / 1000);
		return decodedToken.exp < currentTime;
	}

	private extractRoles(decodedToken: IDecodedAccessToken): TRoleList[] {
		if (typeof decodedToken.resource_access !== "object" || decodedToken.resource_access === null) return [];
		if (!("simp" in decodedToken.resource_access) || !decodedToken.resource_access.simp) return [];
		if (typeof decodedToken.resource_access.simp !== "object") return [];
		const simpResource = decodedToken.resource_access.simp as { roles?: unknown };
		if (!Array.isArray(simpResource.roles)) return [];
		return ((decodedToken.resource_access.simp as { roles: string[] }).roles || [])
			.filter((r): r is TRoleList => Object.values(ROLE_LIST).includes(r as TRoleList))
			.map(r => r as TRoleList);
	}

	private extractPermissions(decodedToken: IDecodedAccessToken): IDefineCaslPermission[] {
		type PermissionPayload = { scopes?: string[]; rsname?: string };
		const validActions = new Set<TPermissionAction>(Object.values(ACTION_LIST));
		const validSubjects = new Set<TPermissionSubject>(Object.values(SUBJECT_LIST));
		const permissions = this.getAuthorizationPermissions(decodedToken) as PermissionPayload[];
		if (!permissions.length) return [];
		return permissions.flatMap(p => {
			const subject = typeof p.rsname === "string" && validSubjects.has(p.rsname as TPermissionSubject) ? (p.rsname as TPermissionSubject) : undefined;
			if (!subject) return [];
			const actions = Array.isArray(p.scopes) ? p.scopes : [];
			return actions
				.filter((a): a is TPermissionAction => validActions.has(a as TPermissionAction))
				.map(action => ({
					action: action as TPermissionAction,
					subject,
				}));
		});
	}

	private getAuthorizationPermissions(decodedToken: IDecodedAccessToken): unknown[] {
		const authorization = decodedToken.authorization;
		if (typeof authorization !== "object" || authorization === null) return [];
		const perms = (authorization as { permissions?: unknown })?.permissions;
		if (!Array.isArray(perms)) return [];
		return perms;
	}

	public clearCache(): void {
		this.tokenCache.clear();
	}

	public getCacheSize(): number {
		return this.tokenCache.size;
	}
}
