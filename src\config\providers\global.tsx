"use client";

import { LogoutLoadingOverlay } from "@/core/auth/components/logout-loading-overlay";
import { UserProviderWithLoading } from "@/core/auth/providers/user.provider";
import { ToastContainer } from "@/core/toast";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import { ThemeProvider } from "@/shared/providers/theme/theme.provider";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { createStore, Provider } from "jotai";
import { Chat } from "../../modules/chat/components";
import { QueryProvider } from "./query";

export const SIMP_STORE = createStore();

export const ProviderGlobal = ({ children }: IReactChildrenType) => {
	return (
		<Provider store={SIMP_STORE}>
			<ThemeProvider enableSSRSafety={true}>
				<QueryProvider>
					<UserProviderWithLoading>
						{children}
						<Chat />
						<ToastContainer />
						<LogoutLoadingOverlay />
					</UserProviderWithLoading>
					<ReactQueryDevtools buttonPosition="top-right" initialIsOpen={false} />
				</QueryProvider>
			</ThemeProvider>
		</Provider>
	);
};
