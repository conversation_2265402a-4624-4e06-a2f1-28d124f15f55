"use client";
import { toast } from "@/core/toast";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";

export const useDeleteKnowledge = () => {
	const queryClient = useQueryClient();
	const { canDelete } = usePermissions();

	const deleteKnowledgeMutation = useMutation<IMessageGlobalReturn, Error, string>({
		mutationKey: chatKeys.custom("delete-knowledge"),
		mutationFn: async (knowledgeId: string) => {
			if (!canDelete("all")) throw new Error("Você não tem permissão para deletar conhecimento. Apenas administradores podem realizar esta ação.");
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(CHAT_ENDPOINTS.REMOVE_KNOWLEDGE(knowledgeId));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => chatKeys.invalidateAll(queryClient),
	});

	return {
		removeKnowledge: (id: string) =>
			toast.promise(deleteKnowledgeMutation.mutateAsync(id), {
				loading: "Removendo conhecimento...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
