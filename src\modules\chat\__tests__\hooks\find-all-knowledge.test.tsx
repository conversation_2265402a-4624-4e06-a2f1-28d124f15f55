import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import { createGetRequest } from "../../../../shared/lib/requests";
import { ApiResponse } from "../../../../shared/types/requests/request.type";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";

import { useFindAllChatKnowledge } from "../../hooks/list/find-all.hook";
import { IChatKnowledgeDto } from "../../types/dtos/find-all-knowledge.dto";

if (!globalThis.fetch) {
	globalThis.fetch = jest.fn();
}

jest.mock("@/shared/lib/requests", () => ({
	createGetRequest: jest.fn(),
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canRead: () => true })),
}));

const mockedCreateGetRequest = createGetRequest as jest.MockedFunction<typeof createGetRequest>;

const testChatKnowledgeData: IChatKnowledgeDto[] = [
	{ id: 1, title: "Setor A", isActive: true, createdAt: new Date("2023-01-01T00:00:00Z") },
	{ id: 2, title: "Setor B", isActive: true, createdAt: new Date("2023-01-02T00:00:00Z") },
	{ id: 3, title: "Setor C", isActive: false, createdAt: new Date("2023-01-03T00:00:00Z") },
];

const mockPaginatedData: IResponsePaginated<IChatKnowledgeDto> = {
	data: testChatKnowledgeData,
	totalItems: 3,
	itemCount: 3,
	itemsPerPage: 10,
	currentPage: 1,
	totalPages: 1,
};

const mockSuccessResponse: ApiResponse<IResponsePaginated<IChatKnowledgeDto>> = {
	success: true,
	data: mockPaginatedData,
	status: 200,
};

const mockEmptyResponse: ApiResponse<IResponsePaginated<IChatKnowledgeDto>> = {
	success: false,
	data: { message: "Nenhum conhecimento encontrado" },
	status: 404,
};

const mockErrorResponse: ApiResponse<IResponsePaginated<IChatKnowledgeDto>> = {
	success: false,
	data: { message: "Erro interno do servidor" },
	status: 500,
};

describe("useFindAllChatKnowledge", () => {
	let queryClient: QueryClient;

	beforeEach(() => {
		queryClient = new QueryClient({
			defaultOptions: {
				queries: {
					retry: false,
				},
			},
		});
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve retornar dados vazios no estado inicial", () => {
		mockedCreateGetRequest.mockImplementation(() => new Promise(() => {}));
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllChatKnowledge(params), { wrapper });
		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.isLoading).toBe(true);
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(false);
	});

	it("deve retornar dados de conhecimento com sucesso", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllChatKnowledge(params), { wrapper });
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});
		expect(result.current.data).toEqual(testChatKnowledgeData);
		expect(result.current.pagination).toEqual({
			totalItems: 3,
			itemsPerPage: 10,
			currentPage: 1,
			totalPages: 1,
		});
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(false);
		expect(result.current.error).toBe(false);
	});

	it("deve lidar com resposta vazia (404)", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockEmptyResponse);
		const params = { page: 1, limit: 10, search: "setor inexistente" };
		const { result } = renderHook(() => useFindAllChatKnowledge(params), { wrapper });
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});
		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(true);
		expect(result.current.error).toBe(false);
	});

	it("deve lidar com erro do servidor", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockErrorResponse);
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllChatKnowledge(params), { wrapper });
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});
		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.hasError).toBe(true);
		expect(result.current.isEmpty).toBe(false);
		expect(result.current.error).toBe("Erro interno do servidor");
	});

	it("deve funcionar com parâmetros de paginação diferentes", async () => {
		const mockPaginatedResponse: ApiResponse<IResponsePaginated<IChatKnowledgeDto>> = {
			success: true,
			data: {
				...mockPaginatedData,
				currentPage: 2,
				itemsPerPage: 5,
				totalPages: 2,
			},
			status: 200,
		};

		mockedCreateGetRequest.mockResolvedValueOnce(mockPaginatedResponse);

		const params = { page: 2, limit: 5, search: "" };
		const { result } = renderHook(() => useFindAllChatKnowledge(params), { wrapper });
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});
		expect(result.current.pagination).toEqual({
			totalItems: 3,
			itemsPerPage: 5,
			currentPage: 2,
			totalPages: 2,
		});
	});

	it("deve funcionar com parâmetro de busca", async () => {
		const mockSearchData: IResponsePaginated<IChatKnowledgeDto> = {
			data: [testChatKnowledgeData[0]],
			totalItems: 1,
			itemCount: 1,
			itemsPerPage: 10,
			currentPage: 1,
			totalPages: 1,
		};
		const mockSearchResponse: ApiResponse<IResponsePaginated<IChatKnowledgeDto>> = {
			success: true,
			data: mockSearchData,
			status: 200,
		};
		mockedCreateGetRequest.mockResolvedValueOnce(mockSearchResponse);
		const params = { page: 1, limit: 10, search: "Conhecimento A" };
		const { result } = renderHook(() => useFindAllChatKnowledge(params), { wrapper });
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});
		expect(result.current.data).toEqual([testChatKnowledgeData[0]]);
		expect(result.current.pagination?.totalItems).toBe(1);
	});

	it("deve funcionar sem parâmetros opcionais", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);
		const params = {};
		const { result } = renderHook(() => useFindAllChatKnowledge(params), { wrapper });
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});
		expect(result.current.data).toEqual(testChatKnowledgeData);
		expect(mockedCreateGetRequest).toHaveBeenCalledWith(expect.stringContaining("/ai/knowledge"));
	});

	it("deve lidar com falha na requisição (erro de rede)", async () => {
		mockedCreateGetRequest.mockRejectedValueOnce(new Error("Erro de rede"));
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllChatKnowledge(params), { wrapper });
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});
		expect(result.current.data).toEqual([]);
		expect(result.current.hasError).toBe(true);
		expect(result.current.isEmpty).toBe(false);
	});
});
