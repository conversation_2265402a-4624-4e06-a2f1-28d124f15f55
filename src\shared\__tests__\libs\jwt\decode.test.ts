import { decodeJWT } from "@/shared/lib/jwt/decode";

const toBase64Url = (input: string) => Buffer.from(input, "utf8").toString("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/g, "");

describe("decodeJWT", () => {
	afterEach(() => {
		jest.restoreAllMocks();
	});

	it("decodifica um JWT válido e retorna o objeto payload", () => {
		const payload = { sub: "123", name: "<PERSON>", admin: true };
		const header = { alg: "none", typ: "JWT" };
		const headerPart = toBase64Url(JSON.stringify(header));
		const payloadPart = toBase64Url(JSON.stringify(payload));
		const signaturePart = "signature";
		const token = `${headerPart}.${payloadPart}.${signaturePart}`;
		const result = decodeJWT<typeof payload>(token);
		expect(result).toEqual(payload);
	});

	it("retorna null quando o token é null", () => {
		expect(decodeJWT(null)).toBeNull();
	});

	it("retorna null para um token malformado (partes faltando)", () => {
		const malformed = "not-a-jwt";
		const result = decodeJWT(malformed);
		expect(result).toBeNull();
	});

	it("retorna null e registra um erro quando o payload não é um JSON válido", () => {
		const errorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
		const headerPart = toBase64Url(JSON.stringify({ alg: "none" }));
		const payloadPart = toBase64Url("not a json");
		const token = `${headerPart}.${payloadPart}.sig`;
		const result = decodeJWT(token);
		expect(result).toBeNull();
		expect(errorSpy).toHaveBeenCalled();
	});
});
