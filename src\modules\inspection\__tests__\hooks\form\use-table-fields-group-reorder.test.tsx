import { useTableFieldsGroupReorder } from "@/modules/inspection/hooks/form/create/table-fields-group-reorder.hook";
import { act, renderHook } from "@testing-library/react";

jest.mock("jotai", () => {
	const actual = jest.requireActual("jotai");
	return {
		...actual,
		useAtomValue: jest.fn(() => [{ tempId: "g1" }, { tempId: "g2" }]),
		useSetAtom: jest.fn(() => jest.fn()),
	};
});

describe("useTableFieldsGroupReorder", () => {
	it("deve permitir mover grupos para cima e para baixo quando possível", async () => {
		const { result } = renderHook(() => useTableFieldsGroupReorder());

		const group = {
			tempId: "g2",
			field: { name: "Nome do Campo" },
			required: false,
			measure: {},
			biFilter: false,
		};

		await act(async () => {
			const payload: Parameters<typeof result.current.handleMoveGroupUp>[0] = {
				items: [group],
				tempId: "g2",
				groupTitle: "Grupo 2",
			};
			await result.current.handleMoveGroupUp(payload);
		});

		expect(result.current.isReordering).toBe(false);
		expect(typeof result.current.canMoveGroupUp).toBe("function");
		expect(typeof result.current.canMoveGroupDown).toBe("function");
	});
});
