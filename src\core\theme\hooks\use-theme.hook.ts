import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { useCallback, useMemo } from "react";
import { 
	IUseThemeHook, 
	ThemeId, 
	CustomThemeId, 
	IThemeConfig 
} from "../types/theme.types";
import {
	currentThemeAtom,
	currentThemeConfigAtom,
	themeStateAtom,
	toggleThemeAtom,
	addCustomThemeAtom,
	updateCustomThemeAtom,
	removeCustomThemeAtom,
	clearThemeErrorAtom,
} from "../atoms/theme.atom";
import {
	persistThemeAtom,
	themePersistenceStatusAtom,
	clearThemePersistenceAtom,
} from "../atoms/theme-persistence.atom";

/**
 * Hook principal para gerenciamento de temas
 * Interface simplificada seguindo princípios SOLID
 */
export function useTheme(): IUseThemeHook {
	const [currentTheme, setCurrentTheme] = useAtom(currentThemeAtom);
	const themeConfig = useAtomValue(currentThemeConfigAtom);
	const themeState = useAtomValue(themeStateAtom);
	const persistenceStatus = useAtomValue(themePersistenceStatusAtom);
	
	const persistTheme = useSetAtom(persistThemeAtom);
	const toggleTheme = useSetAtom(toggleThemeAtom);
	const addCustomTheme = useSetAtom(addCustomThemeAtom);
	const updateCustomTheme = useSetAtom(updateCustomThemeAtom);
	const removeCustomTheme = useSetAtom(removeCustomThemeAtom);
	const clearError = useSetAtom(clearThemeErrorAtom);

	// Função para definir tema com persistência
	const setTheme = useCallback(async (themeId: ThemeId): Promise<void> => {
		try {
			await persistTheme(themeId);
		} catch (error) {
			console.error("Erro ao definir tema:", error);
			throw error;
		}
	}, [persistTheme]);

	// Função para alternar tema
	const handleToggleTheme = useCallback(async (): Promise<void> => {
		try {
			toggleTheme();
		} catch (error) {
			console.error("Erro ao alternar tema:", error);
			throw error;
		}
	}, [toggleTheme]);

	// Função para criar tema customizado
	const createCustomTheme = useCallback(async (
		config: Omit<IThemeConfig, "id" | "isCustom">
	): Promise<IThemeConfig> => {
		try {
			const newTheme = addCustomTheme(config);
			return newTheme;
		} catch (error) {
			console.error("Erro ao criar tema customizado:", error);
			throw error;
		}
	}, [addCustomTheme]);

	// Função para atualizar tema customizado
	const handleUpdateCustomTheme = useCallback(async (
		id: CustomThemeId,
		config: Partial<IThemeConfig>
	): Promise<IThemeConfig> => {
		try {
			const updatedTheme = updateCustomTheme({ id, updates: config });
			return updatedTheme;
		} catch (error) {
			console.error("Erro ao atualizar tema customizado:", error);
			throw error;
		}
	}, [updateCustomTheme]);

	// Função para deletar tema customizado
	const deleteCustomTheme = useCallback(async (id: CustomThemeId): Promise<void> => {
		try {
			removeCustomTheme(id);
		} catch (error) {
			console.error("Erro ao deletar tema customizado:", error);
			throw error;
		}
	}, [removeCustomTheme]);

	// Memoização dos valores retornados
	const returnValue = useMemo((): IUseThemeHook => ({
		currentTheme,
		themeConfig,
		availableThemes: themeState.availableThemes,
		systemPreference: themeState.systemPreference,
		isLoading: themeState.isLoading || persistenceStatus.isSyncing,
		error: themeState.error || persistenceStatus.error,
		setTheme,
		toggleTheme: handleToggleTheme,
		createCustomTheme,
		updateCustomTheme: handleUpdateCustomTheme,
		deleteCustomTheme,
	}), [
		currentTheme,
		themeConfig,
		themeState,
		persistenceStatus,
		setTheme,
		handleToggleTheme,
		createCustomTheme,
		handleUpdateCustomTheme,
		deleteCustomTheme,
	]);

	return returnValue;
}

/**
 * Hook simplificado para apenas alternar entre light/dark
 */
export function useThemeToggle() {
	const { currentTheme, setTheme, isLoading } = useTheme();
	
	const toggle = useCallback(async () => {
		const newTheme = currentTheme === "light" ? "dark" : "light";
		await setTheme(newTheme);
	}, [currentTheme, setTheme]);

	return {
		currentTheme,
		toggle,
		isLoading,
		isDark: currentTheme === "dark",
		isLight: currentTheme === "light",
	};
}

/**
 * Hook para gerenciar apenas temas customizados
 */
export function useCustomThemes() {
	const { availableThemes, createCustomTheme, updateCustomTheme, deleteCustomTheme } = useTheme();
	
	const customThemes = useMemo(
		() => availableThemes.filter(theme => theme.isCustom),
		[availableThemes]
	);

	return {
		customThemes,
		createCustomTheme,
		updateCustomTheme,
		deleteCustomTheme,
		hasCustomThemes: customThemes.length > 0,
	};
}

/**
 * Hook para detectar preferências do sistema
 */
export function useSystemTheme() {
	const { systemPreference, currentTheme, setTheme } = useTheme();
	
	const isSystemTheme = currentTheme === "system";
	const effectiveTheme = isSystemTheme ? systemPreference : currentTheme;
	
	const setSystemTheme = useCallback(async () => {
		await setTheme("system");
	}, [setTheme]);

	return {
		systemPreference,
		isSystemTheme,
		effectiveTheme,
		setSystemTheme,
	};
}

/**
 * Hook para status de persistência
 */
export function useThemePersistence() {
	const persistenceStatus = useAtomValue(themePersistenceStatusAtom);
	const clearPersistence = useSetAtom(clearThemePersistenceAtom);
	
	const handleClearPersistence = useCallback(async () => {
		try {
			await clearPersistence();
		} catch (error) {
			console.error("Erro ao limpar persistência:", error);
			throw error;
		}
	}, [clearPersistence]);

	return {
		...persistenceStatus,
		clearPersistence: handleClearPersistence,
	};
}

/**
 * Hook para validação de temas
 */
export function useThemeValidation() {
	const { availableThemes } = useTheme();
	
	const validateThemeId = useCallback((themeId: ThemeId): boolean => {
		if (!themeId || typeof themeId !== "string") return false;
		
		// Temas padrão
		if (["light", "dark", "system"].includes(themeId)) return true;
		
		// Temas customizados
		return availableThemes.some(theme => theme.id === themeId);
	}, [availableThemes]);

	const validateThemeConfig = useCallback((config: Partial<IThemeConfig>): {
		isValid: boolean;
		errors: string[];
	} => {
		const errors: string[] = [];

		if (!config.name || config.name.trim().length === 0) {
			errors.push("Nome do tema é obrigatório");
		}

		if (config.colors) {
			// Validação básica de cores
			Object.entries(config.colors).forEach(([key, value]) => {
				if (typeof value !== "string" || value.trim().length === 0) {
					errors.push(`Cor "${key}" é inválida`);
				}
			});
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}, []);

	return {
		validateThemeId,
		validateThemeConfig,
	};
}

/**
 * Hook para debugging (apenas em desenvolvimento)
 */
export function useThemeDebug() {
	const themeState = useAtomValue(themeStateAtom);
	const persistenceStatus = useAtomValue(themePersistenceStatusAtom);
	const clearError = useSetAtom(clearThemeErrorAtom);

	if (process.env.NODE_ENV !== "development") {
		return null;
	}

	return {
		themeState,
		persistenceStatus,
		clearError,
		logState: () => {
			console.group("🎨 Theme Debug Info");
			console.log("Current Theme:", themeState.currentTheme);
			console.log("Available Themes:", themeState.availableThemes);
			console.log("System Preference:", themeState.systemPreference);
			console.log("Is Loading:", themeState.isLoading);
			console.log("Error:", themeState.error);
			console.log("Persistence Status:", persistenceStatus);
			console.groupEnd();
		},
	};
}
