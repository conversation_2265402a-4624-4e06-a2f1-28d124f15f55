import { AxiosRequestConfig } from "axios";

interface IHandleResponse<T> {
	success: boolean;
	data: T;
	status: number;
}

export interface IHandleMetadataAxiosRequestConfig extends AxiosRequestConfig {
	_retry?: number;
}

export type TErrorCategory = "validation" | "network" | "authentication" | "authorization" | "business_logic" | "server_error" | "timeout" | "unknown";

export interface IValidationErrorField {
	field: string;
	message: string;
	code?: string;
	value?: unknown;
}

export interface IExtractErrorDetailsResult {
	message: string;
	category: TErrorCategory;
	fields?: IValidationErrorField[];
	code?: string;
	originalError?: unknown;
	context?: Record<string, unknown>;
}

export interface IHandleErrorData {
	message: string;
	method?: string;
	url?: string;
	details?: unknown;
	category?: TErrorCategory;
	fields?: IValidationErrorField[];
	code?: string;
	context?: Record<string, unknown>;
}

export interface IHandleResponseSuccess<T> extends IHandleResponse<T> {
	success: true;
}

export interface IHandleResponseError extends IHandleResponse<IHandleErrorData> {
	success: false;
}

export type ApiResponse<T> = IHandleResponseSuccess<T> | IHandleResponseError;
