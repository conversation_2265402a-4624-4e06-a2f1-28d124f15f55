import { useTableChatAdmin } from "../../../hooks/list/table-chat-admin.hook";
import { EditKnowledgeModal } from "../edit/edit-knowledge-modal";
import { DesktopTableView } from "./desktop-table-view";
import { MobileTableView } from "./mobile-table-view";

interface TableChatAdminProps {
	searchTerm: string;
}

export const TableChatAdmin = ({ searchTerm }: TableChatAdminProps) => {
	const { data, isLoading, error, hasError, pagination, pageSize, isMobile, canUpdate, editKnowledge, handleRowClick, handlePageSizeChange, setCurrentPage } =
		useTableChatAdmin({ searchTerm });

	if (isMobile) {
		return (
			<>
				<MobileTableView
					data={data}
					isLoading={isLoading}
					hasError={hasError}
					error={error}
					searchTerm={searchTerm}
					pagination={pagination}
					onPageChange={setCurrentPage}
					onPageSizeChange={handlePageSizeChange}
				/>
			</>
		);
	}

	return (
		<>
			<DesktopTableView
				data={data}
				isLoading={isLoading}
				hasError={hasError}
				error={error}
				searchTerm={searchTerm}
				pageSize={pageSize}
				pagination={pagination}
				onPageChange={setCurrentPage}
				onPageSizeChange={handlePageSizeChange}
				onRowClick={handleRowClick}
				canUpdate={canUpdate}
			/>
			{editKnowledge.isOpen && editKnowledge.selectedId && (
				<EditKnowledgeModal isOpen={editKnowledge.isOpen} onClose={editKnowledge.closeEditModal} id={editKnowledge.selectedId} />
			)}
		</>
	);
};
