import { useTableFieldsDrag } from "@/modules/inspection/hooks/form/create/table-fields-drag.hook";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { renderHook } from "@testing-library/react";

jest.mock("jotai", () => ({
	atom: jest.fn(initialValue => ({ init: initialValue })),
	useAtomValue: jest.fn(),
	useSetAtom: jest.fn(),
}));

jest.mock("@dnd-kit/core", () => ({
	useSensor: jest.fn(),
	useSensors: jest.fn(() => []),
	MouseSensor: jest.fn(),
	TouchSensor: jest.fn(),
	KeyboardSensor: jest.fn(),
}));

jest.mock("@dnd-kit/sortable", () => ({
	arrayMove: jest.fn((array, from, to) => {
		const result = [...array];
		const [removed] = result.splice(from, 1);
		result.splice(to, 0, removed);
		return result;
	}),
	sortableKeyboardCoordinates: jest.fn(),
}));

interface MockDragEvent {
	active: { id: string };
	over: { id: string } | null;
}

const { useAtomValue, useSetAtom } = jest.requireMock("jotai");

describe("useTableFieldsDrag", () => {
	const mockFields: ICreateFieldForm[] = [
		{
			field: { id: 1, name: "Campo 1" },
			tempId: "field1",
			required: false,
			typeId: 1,
			measure: { id: 1, name: "Unidade 1" },
			biFilter: false,
		},
		{
			field: { id: 2, name: "Campo 2" },
			tempId: "field2",
			required: true,
			typeId: 2,
			measure: { id: 2, name: "Unidade 2" },
			biFilter: false,
		},
	];

	const mockFieldGroups = [
		{
			tempId: "group1",
			groupTitle: "Grupo 1",
			items: mockFields,
		},
	];

	beforeEach(() => {
		jest.clearAllMocks();
		useSetAtom.mockReturnValue(jest.fn());
	});

	it("deve retornar os utilitários de arraste quando grupo existe", () => {
		useAtomValue.mockReturnValue(mockFieldGroups);

		const { result } = renderHook(() => useTableFieldsDrag([], "group1"));

		expect(result.current.dataIdsFields).toEqual(["field1", "field2"]);
		expect(typeof result.current.sortableFieldId).toBe("string");
		expect(typeof result.current.handleDragEndFieldsGroup).toBe("function");
		expect(typeof result.current.sensorsFields).toBe("object");
		expect(Array.isArray(result.current.currentItems)).toBe(true);
		expect(result.current.currentItems).toHaveLength(2);
	});

	it("deve usar items fornecidos quando grupo não existe", () => {
		useAtomValue.mockReturnValue([]);

		const { result } = renderHook(() => useTableFieldsDrag(mockFields, "group-inexistente"));

		expect(result.current.dataIdsFields).toEqual(["field1", "field2"]);
		expect(result.current.currentItems).toEqual(mockFields);
	});

	it("deve chamar setReorderedFields quando drag end é executado com IDs diferentes", () => {
		const mockSetReorderedFields = jest.fn();
		useAtomValue.mockReturnValue(mockFieldGroups);
		useSetAtom.mockReturnValue(mockSetReorderedFields);

		const { result } = renderHook(() => useTableFieldsDrag([], "group1"));

		const mockEvent: MockDragEvent = {
			active: { id: "field1" },
			over: { id: "field2" },
		};

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		result.current.handleDragEndFieldsGroup(mockEvent as any);

		expect(mockSetReorderedFields).toHaveBeenCalledWith({
			groupId: "group1",
			fromIndex: 0,
			toIndex: 1,
		});
	});

	it("não deve chamar setReorderedFields quando active e over têm o mesmo ID", () => {
		const mockSetReorderedFields = jest.fn();
		useAtomValue.mockReturnValue(mockFieldGroups);
		useSetAtom.mockReturnValue(mockSetReorderedFields);

		const { result } = renderHook(() => useTableFieldsDrag([], "group1"));

		const mockEvent: MockDragEvent = {
			active: { id: "field1" },
			over: { id: "field1" },
		};

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		result.current.handleDragEndFieldsGroup(mockEvent as any);

		expect(mockSetReorderedFields).not.toHaveBeenCalled();
	});

	it("não deve chamar setReorderedFields quando over é null", () => {
		const mockSetReorderedFields = jest.fn();
		useAtomValue.mockReturnValue(mockFieldGroups);
		useSetAtom.mockReturnValue(mockSetReorderedFields);

		const { result } = renderHook(() => useTableFieldsDrag([], "group1"));

		const mockEvent: MockDragEvent = {
			active: { id: "field1" },
			over: null,
		};

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		result.current.handleDragEndFieldsGroup(mockEvent as any);

		expect(mockSetReorderedFields).not.toHaveBeenCalled();
	});
});
