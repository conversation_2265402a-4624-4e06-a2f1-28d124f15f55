import { updateField<PERSON>ata<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-values.atom";

import { useFindAllFields } from "@/modules/inspection/hooks/fields/list/find-all.hook";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { GenericSearchSelect } from "@/shared/components/custom/generic-search-select";
import { Row } from "@tanstack/react-table";
import { useSetAtom } from "jotai";
import { FieldValidationWrapper } from "../fields-error-wrapper/field-validation-wrapper";

type Props = { row: Row<ICreateFieldForm>; mode?: "create" | "edit" | "view" };

export const InspectionFormFieldRow = ({ row, mode }: Props) => {
	const { tempId, field } = row.original;
	const updateField = useSetAtom(updateFieldDataAtom);

	const handleChange = (selected: { id: number | string; name: string }) => {
		updateField({
			tempId,
			field: {
				...selected,
				id: typeof selected.id === "string" ? Number(selected.id) : selected.id,
			},
		});
	};

	return (
		<FieldValidationWrapper tempId={tempId} showValidationIcon={false} showErrorMessage={false}>
			<GenericSearchSelect
				disabled={mode === "view"}
				value={{ id: field.id ?? 0, name: field.name }}
				onChange={handleChange}
				useDataHook={useFindAllFields}
				placeholder="Selecione..."
				searchPlaceholder="Buscar campo..."
				loadingText="Carregando campos..."
				emptyText="Nenhum campo encontrado."
				width="w-full"
			/>
		</FieldValidationWrapper>
	);
};
