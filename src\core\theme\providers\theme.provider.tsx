"use client";

import { useEffect, useRef } from "react";
import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import {
	initializeThemeAtom,
	themeHydratedAtom,
	themePersistenceStatusAtom,
	autoSyncThemeAtom,
	themeConflictAtom,
} from "../atoms/theme-persistence.atom";
import { currentThemeAtom, systemPreferenceAtom } from "../atoms/theme.atom";
import { themeService } from "../services/theme.service";

/**
 * Provider de temas para aplicação
 * Gerencia hidratação, sincronização e detecção de mudanças do sistema
 * Compatível com Next.js 15 e Server Components
 */

interface IThemeProviderProps extends IReactChildrenType {
	/**
	 * Tema inicial para SSR (opcional)
	 * Se não fornecido, será detectado automaticamente
	 */
	initialTheme?: string;
	
	/**
	 * Habilita sincronização automática
	 * @default true
	 */
	enableAutoSync?: boolean;
	
	/**
	 * Habilita detecção de mudanças na preferência do sistema
	 * @default true
	 */
	enableSystemDetection?: boolean;
	
	/**
	 * Callback chamado quando o tema é inicializado
	 */
	onThemeInitialized?: (theme: string) => void;
	
	/**
	 * Callback chamado quando há erro na inicialização
	 */
	onInitializationError?: (error: string) => void;
}

export function ThemeProvider({
	children,
	initialTheme,
	enableAutoSync = true,
	enableSystemDetection = true,
	onThemeInitialized,
	onInitializationError,
}: IThemeProviderProps) {
	const [currentTheme] = useAtom(currentThemeAtom);
	const isHydrated = useAtomValue(themeHydratedAtom);
	const persistenceStatus = useAtomValue(themePersistenceStatusAtom);
	const initializeTheme = useSetAtom(initializeThemeAtom);
	const enableAutoSyncAction = useSetAtom(autoSyncThemeAtom);
	const checkThemeConflict = useSetAtom(themeConflictAtom);
	const setSystemPreference = useSetAtom(systemPreferenceAtom);
	
	const initializationRef = useRef(false);
	const systemListenerRef = useRef<(() => void) | null>(null);

	// Inicialização do tema
	useEffect(() => {
		if (initializationRef.current) return;
		initializationRef.current = true;

		const initialize = async () => {
			try {
				// Se há tema inicial (SSR), aplica primeiro
				if (initialTheme) {
					themeService.applyThemeToDOM(initialTheme);
				}

				// Inicializa o sistema de temas
				await initializeTheme();
				
				// Verifica conflitos entre servidor e cliente
				const conflictResult = await checkThemeConflict();
				if (conflictResult?.hasConflict) {
					console.warn("Conflito de tema resolvido:", conflictResult);
				}

				// Callback de sucesso
				if (onThemeInitialized) {
					onThemeInitialized(currentTheme);
				}
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : "Erro na inicialização";
				console.error("Erro na inicialização do tema:", error);
				
				if (onInitializationError) {
					onInitializationError(errorMessage);
				}
			}
		};

		initialize();
	}, [
		initialTheme,
		initializeTheme,
		checkThemeConflict,
		currentTheme,
		onThemeInitialized,
		onInitializationError,
	]);

	// Configuração da sincronização automática
	useEffect(() => {
		if (!isHydrated || !enableAutoSync) return;

		const cleanup = enableAutoSyncAction(true);
		return cleanup;
	}, [isHydrated, enableAutoSync, enableAutoSyncAction]);

	// Detecção de mudanças na preferência do sistema
	useEffect(() => {
		if (!enableSystemDetection || typeof window === "undefined") return;

		try {
			const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
			
			// Define preferência inicial
			setSystemPreference(mediaQuery.matches ? "dark" : "light");
			
			// Listener para mudanças
			const handleChange = (e: MediaQueryListEvent) => {
				const newPreference = e.matches ? "dark" : "light";
				setSystemPreference(newPreference);
				
				// Se o tema atual é "system", reaplica automaticamente
				if (currentTheme === "system") {
					themeService.applyThemeToDOM("system");
				}
			};

			mediaQuery.addEventListener("change", handleChange);
			systemListenerRef.current = () => mediaQuery.removeEventListener("change", handleChange);

			return () => {
				if (systemListenerRef.current) {
					systemListenerRef.current();
					systemListenerRef.current = null;
				}
			};
		} catch (error) {
			console.warn("Não foi possível configurar detecção de preferência do sistema:", error);
		}
	}, [enableSystemDetection, currentTheme, setSystemPreference]);

	// Configuração do listener de preferência do sistema no serviço
	useEffect(() => {
		if (!enableSystemDetection) return;

		const cleanup = themeService.setupSystemPreferenceListener();
		return cleanup;
	}, [enableSystemDetection]);

	// Aplicação do tema quando muda
	useEffect(() => {
		if (!isHydrated || !currentTheme) return;

		themeService.applyThemeToDOM(currentTheme);
	}, [currentTheme, isHydrated]);

	// Cleanup na desmontagem
	useEffect(() => {
		return () => {
			if (systemListenerRef.current) {
				systemListenerRef.current();
			}
		};
	}, []);

	// Renderização com informações de debug em desenvolvimento
	if (process.env.NODE_ENV === "development" && !isHydrated) {
		return (
			<div style={{ 
				position: "fixed", 
				top: 0, 
				left: 0, 
				right: 0, 
				bottom: 0, 
				backgroundColor: "var(--background, #ffffff)", 
				color: "var(--foreground, #000000)",
				display: "flex",
				alignItems: "center",
				justifyContent: "center",
				zIndex: 9999,
				fontSize: "14px",
			}}>
				<div style={{ textAlign: "center" }}>
					<div>🎨 Inicializando sistema de temas...</div>
					{persistenceStatus.error && (
						<div style={{ color: "red", marginTop: "8px" }}>
							Erro: {persistenceStatus.error}
						</div>
					)}
				</div>
			</div>
		);
	}

	return <>{children}</>;
}

/**
 * Provider de temas com configuração padrão
 * Para uso simples sem configurações específicas
 */
export function SimpleThemeProvider({ children }: IReactChildrenType) {
	return (
		<ThemeProvider
			enableAutoSync={true}
			enableSystemDetection={true}
		>
			{children}
		</ThemeProvider>
	);
}

/**
 * Provider de temas para SSR
 * Recebe tema inicial do servidor para evitar flash
 */
interface ISSRThemeProviderProps extends IReactChildrenType {
	serverTheme: string;
}

export function SSRThemeProvider({ children, serverTheme }: ISSRThemeProviderProps) {
	return (
		<ThemeProvider
			initialTheme={serverTheme}
			enableAutoSync={true}
			enableSystemDetection={true}
			onThemeInitialized={(theme) => {
				console.log(`Tema inicializado: ${theme}`);
			}}
			onInitializationError={(error) => {
				console.error(`Erro na inicialização do tema: ${error}`);
			}}
		>
			{children}
		</ThemeProvider>
	);
}

/**
 * HOC para componentes que precisam aguardar a hidratação do tema
 */
export function withThemeHydration<T extends object>(
	Component: React.ComponentType<T>
) {
	return function ThemeHydratedComponent(props: T) {
		const isHydrated = useAtomValue(themeHydratedAtom);
		
		if (!isHydrated) {
			return (
				<div style={{ 
					opacity: 0.5, 
					pointerEvents: "none",
					transition: "opacity 200ms ease",
				}}>
					<Component {...props} />
				</div>
			);
		}
		
		return <Component {...props} />;
	};
}
