import RefreshService from "@/core/auth/services/refresh-service";
import { AxiosInstance } from "axios";

const toError = (err: unknown): Error => {
	if (err instanceof Error) return err;
	try {
		return new Error(JSON.stringify(err));
	} catch {
		return new Error(String(err));
	}
};

const isErrorWithResponse = (err: unknown): err is { response?: { status?: number } } => {
	return Boolean(err && typeof err === "object" && "response" in err);
};

const isRequestLike = (obj: unknown): obj is { _retry?: boolean; url?: string; config?: unknown } => {
	return Boolean(obj && typeof obj === "object" && ("_retry" in (obj as Record<string, unknown>) || "url" in (obj as Record<string, unknown>)));
};

const shouldAttemptRefresh = (error: unknown, originalRequest: unknown, refreshService: RefreshService): boolean => {
	const isUnauthorized = isErrorWithResponse(error) && error.response?.status === 401;
	const notRetried = isRequestLike(originalRequest) ? !originalRequest._retry : false;
	if (!isUnauthorized || !isRequestLike(originalRequest)) return false;
	const url = originalRequest.url;
	if (!url) return false;
	const refreshable = !refreshService.isNonRefreshableEndpoint(url);
	return Boolean(notRetried && refreshable);
};

export const setupRefreshInterceptor = (instance: AxiosInstance): void => {
	const refreshService = new RefreshService(instance);

	instance.interceptors.response.use(
		response => response,
		async error => {
			const originalRequest = error?.config;

			if (shouldAttemptRefresh(error, originalRequest, refreshService)) {
				try {
					return await refreshService.handleTokenRefresh(originalRequest);
				} catch (refreshError) {
					return Promise.reject(toError(refreshError));
				}
			}

			return Promise.reject(toError(error));
		},
	);
};
