import { Badge } from "@/shared/components/shadcn/badge";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Link, Trash } from "lucide-react";
import { Button } from "../../../../../../../shared/components/shadcn/button";
import { IFormLinkDto } from "../../../../../types/form-link/dtos/find-all.dto";
import { ConfirmDeleteFormLinkModal } from "../delete/confirm-delete-modal";

interface IFormsLinksCardMobileProps {
	link: IFormLinkDto;
}

export const FormsLinksCardMobile = ({ link }: IFormsLinksCardMobileProps) => {
	const deleteModal = useModal();

	return (
		<>
			<Card className="relative border bg-white shadow-sm hover:shadow-md">
				<CardContent>
					<div className="mb-3 flex items-start justify-between">
						<h3 className="mr-2 line-clamp-2 flex-1 text-base leading-tight font-semibold">
							{link.form}
							<Badge variant="secondary" className="ml-2 text-xs">
								<Link className="h-3 w-3" />
							</Badge>
						</h3>
						<div className="bg-primary h-1.5 w-1.5 rounded-full opacity-60" />
					</div>
					<div className="mb-4 space-y-2">
						<div className="flex items-center justify-between">
							<span className="text-xs text-gray-500">Célula</span>
							<Badge variant="outline" className="bg-gray-50 px-2 py-0.5 font-mono text-xs text-gray-700">
								{link.cell}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span className="text-xs text-gray-500">Atividade</span>
							<Badge variant="outline" className="bg-gray-50 px-2 py-0.5 font-mono text-xs text-gray-700">
								{link.activity}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span className="text-xs text-gray-500">Vinculado em</span>
							<span className="bg-primary flex h-5 w-20 items-center justify-center rounded text-xs font-medium text-white">{link.linkedAt}</span>
						</div>
					</div>
					<Separator className="my-4" />
					<div className="flex gap-1.5">
						<Button
							size="sm"
							variant="outline"
							onClick={deleteModal.openModal}
							className="h-8 flex-1 border-none bg-red-500/10 px-2 text-xs font-medium text-red-500"
						>
							<Trash className="h-3 w-3" />
							<span>Excluir</span>
						</Button>
					</div>
				</CardContent>
			</Card>
			<ConfirmDeleteFormLinkModal
				isOpen={deleteModal.isOpen}
				onClose={deleteModal.closeModal}
				name={`${link.form} - ${link.cell}`}
				linkId={String(link.id)}
			/>
		</>
	);
};
