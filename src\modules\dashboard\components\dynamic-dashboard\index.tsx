"use client";

import { TRoleList } from "@/config/permissions";
import { getUserMainRole } from "@/core/auth/atoms/user.atom";
import { useAtomValue } from "jotai";
import { JSX } from "react";
import { DashboardAdminScreen } from "../../screens/admin";

export const DynamicDashboard = () => {
	const role = useAtomValue(getUserMainRole);
	if (!role) return null;
	const roleScreens: Record<TRoleList, JSX.Element> = {
		Administrador: <DashboardAdminScreen />,
		Técnico: <>Técnico tela</>,
		ADV: <>ADV tela</>,
	};
	return roleScreens[role] || null;
};
