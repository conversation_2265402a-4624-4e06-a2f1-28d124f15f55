import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { IPaginationParameters } from "@/shared/types/pagination/types";
import { useQuery } from "@tanstack/react-query";
import { createGetRequest } from "../../../../shared/lib/requests";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";
import { SECTOR_ENDPOINTS } from "../../api/endpoints";
import { sectorQueryKeys } from "../../constants/query";
import { SECTOR_SUBJECTS } from "../../constants/subjects";
import { ISectorDto } from "../../types/find-all.dto";

export const useFindAllSector = ({ page, limit, search }: IPaginationParameters) => {
	const { canRead } = usePermissions();
	const { data, isLoading, isFetched } = useQuery({
		queryKey: sectorQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<ISectorDto>>(SECTOR_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(SECTOR_SUBJECTS.SECTOR),
	});

	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
