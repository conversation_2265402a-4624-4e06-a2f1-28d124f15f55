import { Button } from "@/shared/components/shadcn/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/shadcn/popover";
import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { cn } from "@/shared/lib/shadcn/utils";
import { AlertCircleIcon, CheckIcon, ChevronsUpDownIcon, LoaderIcon, SearchIcon } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

export interface ISelectValue {
	id: string | number;
	name: string;
}
type BaseDataItem = {
	id: string | number;
};

interface IUseDataHookResult<T extends BaseDataItem = BaseDataItem> {
	data: T[];
	isLoading: boolean;
	hasError: boolean;
	error: string | false | undefined;
}

interface IGenericSelectProps<T extends BaseDataItem = BaseDataItem> {
	value: ISelectValue;
	onChange: (value: ISelectValue) => void;
	useDataHook: (params: { limit: number; search: string; page: number }) => IUseDataHookResult<T>;
	displayField?: keyof T | ((item: T) => string);
	placeholder?: string;
	searchPlaceholder?: string;
	loadingText?: string;
	emptyText?: string;
	width?: string;
	disabled?: boolean;
}

export const GenericSearchSelect = <T extends BaseDataItem>({
	value,
	onChange,
	useDataHook,
	displayField = "name" as keyof T,
	placeholder = "Selecione...",
	searchPlaceholder = "Buscar...",
	loadingText = "Carregando...",
	emptyText = "Nenhum item encontrado.",
	width = "w-[200px]",
	disabled = false,
}: IGenericSelectProps<T>) => {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
	const buttonRef = useRef<HTMLButtonElement>(null);
	const [buttonWidth, setButtonWidth] = useState<string>("");

	useDebounce((term: unknown) => setDebouncedSearchTerm(term as string), { delay: 300 })(searchTerm);

	useEffect(() => {
		if (buttonRef.current) {
			setButtonWidth(`${buttonRef.current.offsetWidth}px`);
		}
	}, [open, width]);

	const { data, isLoading, hasError, error } = useDataHook({
		limit: 50,
		search: debouncedSearchTerm,
		page: 1,
	});

	const getDisplayText = useCallback(
		(item: T): string => {
			if (typeof displayField === "function") {
				return displayField(item);
			}
			const fieldValue = item[displayField];
			if (fieldValue === null || fieldValue === undefined) {
				return "";
			}
			return typeof fieldValue === "string" ? fieldValue : String(fieldValue);
		},
		[displayField],
	);

	const getItemId = useCallback((item: T): string | number => {
		return item?.id;
	}, []);

	const handleItemSelect = useCallback(
		(item: T) => {
			const itemId = getItemId(item);
			const displayText = getDisplayText(item);
			onChange({ id: typeof itemId === "string" ? itemId : Number(itemId), name: displayText });
			setOpen(false);
		},
		[onChange, getItemId, getDisplayText],
	);

	const renderedItems = useMemo(() => {
		if (!data.length) return null;

		return data.map(item => {
			const itemId = getItemId(item);
			const displayText = getDisplayText(item);
			const isSelected = value?.id === itemId || value?.id === Number(itemId);

			return (
				<div
					key={itemId}
					className={cn(
						"flex cursor-pointer items-center rounded-sm px-2 py-1.5 text-sm select-none",
						"hover:bg-accent hover:text-accent-foreground",
					)}
					onClick={() => handleItemSelect(item)}
				>
					<CheckIcon className={cn("mr-2 h-4 w-4", isSelected ? "opacity-100" : "opacity-0")} />
					{displayText}
				</div>
			);
		});
	}, [data, value?.id, getItemId, getDisplayText, handleItemSelect]);

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					ref={buttonRef}
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className={`${width} justify-between ${disabled ? "cursor-default bg-gray-100" : ""}`}
					disabled={isLoading || disabled}
				>
					{value?.id ? getDisplayText((data.find(item => getItemId(item) === value?.id) as T) ?? (value as unknown as T)) : placeholder}
					<ChevronsUpDownIcon className="ml-2 h-4 w-4 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent style={{ width: buttonWidth, minWidth: buttonWidth, maxWidth: buttonWidth }} className="p-0">
				<div className="flex flex-col">
					<div className="flex items-center border-b px-3 py-2">
						<SearchIcon className="mr-2 h-4 w-4 opacity-50" />
						<input
							type="text"
							placeholder={searchPlaceholder}
							value={searchTerm}
							onChange={e => setSearchTerm(e.target.value)}
							className={`placeholder:text-muted-foreground w-full bg-transparent outline-none ${disabled ? "cursor-default bg-gray-100" : ""}`}
							disabled={disabled}
						/>
					</div>
					<div className="max-h-[200px] overflow-auto">
						{isLoading && (
							<div className="py-6 text-center text-sm">
								<LoaderIcon className="mx-auto mb-2 h-4 w-4 animate-spin" />
								<span className="text-muted-foreground">{loadingText}</span>
							</div>
						)}
						{hasError && (
							<div className="py-6 text-center text-sm">
								<AlertCircleIcon className="text-destructive mx-auto mb-2 h-4 w-4" />
								<span className="text-destructive">{error}</span>
							</div>
						)}
						{!isLoading &&
							!hasError &&
							(data.length > 0 ? (
								<div className="p-1">{renderedItems}</div>
							) : (
								<div className="text-muted-foreground py-6 text-center text-sm">{emptyText}</div>
							))}
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
};
