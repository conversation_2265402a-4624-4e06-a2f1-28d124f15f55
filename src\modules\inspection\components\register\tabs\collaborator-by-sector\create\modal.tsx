import { TCreateCollabSectorSchema } from "@/modules/inspection/validators/collaborator-by-sector/create";
import { Modal } from "@/shared/components/custom/modal";
import { useCreateCollabBySectorMutation } from "@/modules/inspection/hooks/collaborator-by-sector/create/mutation.hook";
import FormCreateCollabBysector from "./form";
import useCreateCollabSectorForm from "@/modules/inspection/hooks/collaborator-by-sector/create/collab-sector.hook";

interface IModalCreateFormCollabSector {
	isOpen: boolean;
	onClose: () => void;
}

export default function ModalCreateCollabSector({ isOpen, onClose }: IModalCreateFormCollabSector) {
	const { methods } = useCreateCollabSectorForm();
	const { createCollabBySectorType } = useCreateCollabBySectorMutation();

	function handleSubmit(data: TCreateCollabSectorSchema) {
		const payload = {
			collabDoc: data.collaborator.id,
			sectorId: data.sector.id,
			pin: data.pin,
		};
		createCollabBySectorType(payload);
		onClose();
		methods.reset();
	}

	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[500px] !max-w-none" title="Vincular colaborador no setor">
			<FormCreateCollabBysector methods={methods} onSubmit={handleSubmit} onClose={onClose} />
		</Modal>
	);
}
