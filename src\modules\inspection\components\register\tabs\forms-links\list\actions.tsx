import { Trash } from "lucide-react";
import { Button } from "../../../../../../../shared/components/shadcn/button";
import { useModal } from "../../../../../../../shared/hooks/utils/modal.hook";
import { ConfirmDeleteFormLinkModal } from "../delete/confirm-delete-modal";

export const CellComponentActions = ({ linkId, name }: { linkId: string; name: string }) => {
	const deleteModal = useModal();

	return (
		<div className="pr-[10px] text-end">
			<Button size="icon" onClick={deleteModal.openModal} className="group ml-2 h-8 w-8 bg-red-500/5 hover:bg-red-500/30">
				<Trash className="h-4 w-4 text-red-500 group-hover:text-white" />
			</Button>
			<ConfirmDeleteFormLinkModal isOpen={deleteModal.isOpen} onClose={deleteModal.closeModal} name={name} linkId={linkId} />
		</div>
	);
};
