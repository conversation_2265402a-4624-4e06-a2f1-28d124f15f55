import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { useAuthCheck } from "../../hooks/auth/auth-check.hook";
import * as authActions from "../../lib/auth-actions";

jest.mock("../../lib/auth-actions", () => ({
	getAuthToken: jest.fn(),
}));

function TestComponent() {
	const { hasToken, isChecking, checkAuth } = useAuthCheck();
	return (
		<div>
			<span data-testid="hasToken">{String(hasToken)}</span>
			<span data-testid="isChecking">{String(isChecking)}</span>
			<button onClick={checkAuth}>verificar</button>
		</div>
	);
}

describe("hook useAuthCheck", () => {
	afterEach(() => {
		jest.resetAllMocks();
	});

	it("inicia checando no mount e define hasToken true quando o token existe", async () => {
		(authActions.getAuthToken as jest.Mock).mockResolvedValue("token-value");

		render(<TestComponent />);

		expect(screen.getByTestId("isChecking").textContent).toBe("true");
		expect(screen.getByTestId("hasToken").textContent).toBe("null");

		await waitFor(() => expect(screen.getByTestId("isChecking").textContent).toBe("false"));
		expect(screen.getByTestId("hasToken").textContent).toBe("true");
	});

	it("define hasToken como false quando nenhum token é retornado", async () => {
		(authActions.getAuthToken as jest.Mock).mockResolvedValue(null);

		render(<TestComponent />);

		await waitFor(() => expect(screen.getByTestId("isChecking").textContent).toBe("false"));
		expect(screen.getByTestId("hasToken").textContent).toBe("false");
	});

	it("trata getAuthToken lançando erro e define hasToken como false", async () => {
		(authActions.getAuthToken as jest.Mock).mockRejectedValue(new Error("fail"));
		const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

		render(<TestComponent />);

		await waitFor(() => expect(screen.getByTestId("isChecking").textContent).toBe("false"));
		expect(screen.getByTestId("hasToken").textContent).toBe("false");

		consoleSpy.mockRestore();
	});

	it("checkAuth pode ser chamado manualmente para reexecutar a verificação do token", async () => {
		const getTokenMock = (authActions.getAuthToken as jest.Mock).mockResolvedValueOnce(null).mockResolvedValueOnce("new-token");

		render(<TestComponent />);

		await waitFor(() => expect(screen.getByTestId("isChecking").textContent).toBe("false"));
		expect(screen.getByTestId("hasToken").textContent).toBe("false");

		fireEvent.click(screen.getByText("verificar"));

		await waitFor(() => expect(screen.getByTestId("isChecking").textContent).toBe("false"));
		expect(screen.getByTestId("hasToken").textContent).toBe("true");

		expect(getTokenMock).toHaveBeenCalledTimes(2);
	});
});
