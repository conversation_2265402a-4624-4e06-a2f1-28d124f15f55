"use client";
import { useCallback, useState } from "react";

interface PaginationProps {
	initialPage?: number;
	initialLimit?: number;
	total?: number;
}

export const usePagination = ({ initialPage = 1, initialLimit = 10, total = 0 }: PaginationProps = {}) => {
	const [currentPage, setCurrentPage] = useState(initialPage);
	const [limit, setLimit] = useState(initialLimit);
	const [pageSize, setPageSize] = useState(initialLimit);

	const isFirstPage = currentPage === 1;
	const isLastPage = currentPage === Math.ceil(total / limit);

	const nextPage = useCallback(() => {
		setCurrentPage(p => (p < Math.ceil(total / limit) ? p + 1 : p));
	}, [total, limit]);

	const previousPage = useCallback(() => {
		setCurrentPage(p => (p > 1 ? p - 1 : p));
	}, []);

	const goToPage = useCallback((page: number) => setCurrentPage(Math.max(1, Math.min(page, Math.ceil(total / limit)))), [total, limit]);

	const changeLimit = useCallback(
		(newLimit: number) => {
			setLimit(newLimit);
			const newTotalPages = Math.max(1, Math.ceil(total / newLimit));
			setCurrentPage(p => (p > newTotalPages ? newTotalPages : p));
		},
		[total],
	);

	const setItemsPerPage = useCallback((size: number) => {
		setPageSize(size);
		setCurrentPage(1);
	}, []);

	return {
		currentPage,
		setCurrentPage,
		limit,
		isFirstPage,
		isLastPage,
		nextPage,
		previousPage,
		goToPage,
		changeLimit,
		setItemsPerPage,
		pageSize,
	};
};
