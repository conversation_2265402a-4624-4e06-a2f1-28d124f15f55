import { handleStreamingChunk } from "../../lib/handler-chunk.lib";
import { IChatStreamResponse } from "../../types/streaming.type";

describe("handleStreamingChunk", () => {
	const assistantMessageId = "assistant-message-id";
	const currentValue = "Current value";
	let updateMessage: jest.Mock;

	beforeEach(() => {
		updateMessage = jest.fn();
	});

	const callHandler = (chunkData: IChatStreamResponse) =>
		handleStreamingChunk({
			chunkData,
			updateMessage,
			assistantMessageId,
			currentValue,
		});

	it("deve chamar updateMessage com isStreaming = true quando type !== 'complete'", () => {
		callHandler({ content: "New content" });
		expect(updateMessage).toHaveBeenCalledWith(assistantMessageId, {
			content: currentValue,
			isStreaming: true,
		});
	});

	it("deve chamar updateMessage com isStreaming = false quando type === 'complete'", () => {
		callHandler({ type: "complete" } as IChatStreamResponse);
		expect(updateMessage).toHaveBeenCalledWith(assistantMessageId, {
			content: currentValue,
			isStreaming: false,
		});
	});
});
