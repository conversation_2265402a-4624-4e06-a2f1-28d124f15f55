import { IThemeConfig, IThemePersistenceConfig, THEME_CONSTANTS } from "../types/theme.types";

/**
 * Configurações dos temas padrão do sistema
 * Baseado nas cores já existentes no projeto
 */

// Tema Light (baseado no :root atual)
export const LIGHT_THEME: IThemeConfig = {
	id: "light",
	name: "Clar<PERSON>",
	description: "Tema claro padrão do sistema",
	isCustom: false,
	isSystemDefault: true,
	colors: {
		// Cores principais
		primary: "#004475",
		primaryForeground: "#ffffff",
		secondary: "#f1f5f9",
		secondaryForeground: "#1e293b",

		// Cores de fundo
		background: "#f9f9fa",
		backgroundSecondary: "#eceef2",
		foreground: "#000000",

		// Cores de texto
		textPrimary: "#000000",
		textSecondary: "#514d4d",

		// Cores de borda e input
		border: "#e0e0e0",
		input: "#e0e0e0",
		ring: "#004475",

		// Cores de acento
		accent: "#f1f5f9",
		accentForeground: "#1e293b",
		muted: "#f1f5f9",
		mutedForeground: "#64748b",

		// Cores de destruição
		destructive: "#ef4444",
		destructiveForeground: "#ffffff",

		// Cores específicas
		leafGreenColor: "#00a03c",

		// Cores de card
		card: "#ffffff",
		cardForeground: "#000000",

		// Cores de popover
		popover: "#ffffff",
		popoverForeground: "#000000",

		// Cores de chart
		chart1: "#3b82f6",
		chart2: "#10b981",
		chart3: "#f59e0b",
		chart4: "#ef4444",
		chart5: "#8b5cf6",

		// Cores de sidebar
		sidebar: "#ffffff",
		sidebarForeground: "#000000",
		sidebarPrimary: "#004475",
		sidebarPrimaryForeground: "#ffffff",
		sidebarAccent: "#f1f5f9",
		sidebarAccentForeground: "#1e293b",
		sidebarBorder: "#e0e0e0",
		sidebarRing: "#004475",
	},
};

// Tema Dark (baseado no .dark atual)
export const DARK_THEME: IThemeConfig = {
	id: "dark",
	name: "Escuro",
	description: "Tema escuro para reduzir fadiga visual",
	isCustom: false,
	colors: {
		// Cores principais
		primary: "#004475",
		primaryForeground: "#ffffff",
		secondary: "#1e293b",
		secondaryForeground: "#f1f5f9",

		// Cores de fundo
		background: "#000000",
		backgroundSecondary: "#0f172a",
		foreground: "#ffffff",

		// Cores de texto
		textPrimary: "#ffffff",
		textSecondary: "#cbd5e1",

		// Cores de borda e input
		border: "#334155",
		input: "#334155",
		ring: "#004475",

		// Cores de acento
		accent: "#1e293b",
		accentForeground: "#f1f5f9",
		muted: "#1e293b",
		mutedForeground: "#94a3b8",

		// Cores de destruição
		destructive: "#ef4444",
		destructiveForeground: "#ffffff",

		// Cores específicas
		leafGreenColor: "#00a03c",

		// Cores de card
		card: "#0f172a",
		cardForeground: "#ffffff",

		// Cores de popover
		popover: "#0f172a",
		popoverForeground: "#ffffff",

		// Cores de chart
		chart1: "#3b82f6",
		chart2: "#10b981",
		chart3: "#f59e0b",
		chart4: "#ef4444",
		chart5: "#8b5cf6",

		// Cores de sidebar
		sidebar: "#0f172a",
		sidebarForeground: "#ffffff",
		sidebarPrimary: "#004475",
		sidebarPrimaryForeground: "#ffffff",
		sidebarAccent: "#1e293b",
		sidebarAccentForeground: "#f1f5f9",
		sidebarBorder: "#334155",
		sidebarRing: "#004475",
	},
};

// Tema System (detecta automaticamente)
export const SYSTEM_THEME: IThemeConfig = {
	id: "system",
	name: "Sistema",
	description: "Segue a preferência do sistema operacional",
	isCustom: false,
	colors: LIGHT_THEME.colors, // Fallback para light
};

// Tema customizado de exemplo (azul corporativo)
export const CORPORATE_BLUE_THEME: IThemeConfig = {
	id: "corporate-blue",
	name: "Azul Corporativo",
	description: "Tema corporativo com tons de azul",
	isCustom: true,
	colors: {
		...LIGHT_THEME.colors,
		primary: "#1e40af",
		primaryForeground: "#ffffff",
		background: "#f8fafc",
		backgroundSecondary: "#e2e8f0",
		accent: "#dbeafe",
		accentForeground: "#1e40af",
	},
};

// Lista de todos os temas padrão
export const DEFAULT_THEMES: IThemeConfig[] = [
	LIGHT_THEME,
	DARK_THEME,
	SYSTEM_THEME,
];

// Lista de temas customizados de exemplo
export const EXAMPLE_CUSTOM_THEMES: IThemeConfig[] = [
	CORPORATE_BLUE_THEME,
];

// Configuração de persistência
export const THEME_PERSISTENCE_CONFIG: IThemePersistenceConfig = {
	cookieName: "simp-theme",
	localStorageKey: "simp-theme-preference",
	version: THEME_CONSTANTS.STORAGE_VERSION,
	maxAge: THEME_CONSTANTS.COOKIE_MAX_AGE,
};

// Mapeamento de CSS variables para propriedades do tema
export const CSS_VARIABLE_MAP: Record<keyof IThemeConfig["colors"], string> = {
	primary: "--primary",
	primaryForeground: "--primary-foreground",
	secondary: "--secondary",
	secondaryForeground: "--secondary-foreground",
	background: "--background",
	backgroundSecondary: "--background-secondary",
	foreground: "--foreground",
	textPrimary: "--text-primary",
	textSecondary: "--text-secondary",
	border: "--border",
	input: "--input",
	ring: "--ring",
	accent: "--accent",
	accentForeground: "--accent-foreground",
	muted: "--muted",
	mutedForeground: "--muted-foreground",
	destructive: "--destructive",
	destructiveForeground: "--destructive-foreground",
	leafGreenColor: "--leaf-green-color",
	card: "--card",
	cardForeground: "--card-foreground",
	popover: "--popover",
	popoverForeground: "--popover-foreground",
	chart1: "--chart-1",
	chart2: "--chart-2",
	chart3: "--chart-3",
	chart4: "--chart-4",
	chart5: "--chart-5",
	sidebar: "--sidebar",
	sidebarForeground: "--sidebar-foreground",
	sidebarPrimary: "--sidebar-primary",
	sidebarPrimaryForeground: "--sidebar-primary-foreground",
	sidebarAccent: "--sidebar-accent",
	sidebarAccentForeground: "--sidebar-accent-foreground",
	sidebarBorder: "--sidebar-border",
	sidebarRing: "--sidebar-ring",
};

// Classes CSS para aplicação de temas
export const THEME_CSS_CLASSES = {
	light: "theme-light",
	dark: "theme-dark dark",
	system: "theme-system",
} as const;

// Transições CSS para mudança de tema
export const THEME_TRANSITION_CSS = `
	* {
		transition: background-color ${THEME_CONSTANTS.TRANSITION_DURATION}ms ease,
		           border-color ${THEME_CONSTANTS.TRANSITION_DURATION}ms ease,
		           color ${THEME_CONSTANTS.TRANSITION_DURATION}ms ease;
	}
`;
