import { CookieHeaderService } from "@/shared/lib/cookies/services/cookie-header.service";

describe("CookieHeaderService", () => {
	let service: CookieHeaderService;

	beforeEach(() => {
		service = new CookieHeaderService();
	});

	describe("sanitizeCookieValue", () => {
		it("remove caracteres de controle e remove espaços em branco nas extremidades", () => {
			// @ts-expect-error testando método privado
			expect(service.sanitizeCookieValue(" value\r\n\t ")).toBe("value");
		});

		it("codifica valores com caracteres não ASCII ou especiais", () => {
			// @ts-expect-error testando método privado
			expect(service.sanitizeCookieValue("val;ue")).toBe(encodeURIComponent("val;ue"));
			// @ts-expect-error testando método privado
			expect(service.sanitizeCookieValue("val,ue")).toBe(encodeURIComponent("val,ue"));
			// @ts-expect-error testando método privado
			expect(service.sanitizeCookieValue('val"ue')).toBe(encodeURIComponent('val"ue'));
			// @ts-expect-error testando método privado
			expect(service.sanitizeCookieValue("val\\ue")).toBe(encodeURIComponent("val\\ue"));
			// @ts-expect-error testando método privado
			expect(service.sanitizeCookieValue("välue")).toBe(encodeURIComponent("välue"));
		});

		it("retorna valor sanitizado se nenhum encoding for necessário", () => {
			// @ts-expect-error testando método privado
			expect(service.sanitizeCookieValue("simpleValue")).toBe("simpleValue");
		});
	});

	describe("shouldIncludeCookie", () => {
		it("retorna falso para nomes de cookie excluídos", () => {
			expect(service.shouldIncludeCookie("__next_hmr_refresh_hash__", "value")).toBe(false);
			expect(service.shouldIncludeCookie("__next_hmr_refresh_hash", "value")).toBe(false);
			expect(service.shouldIncludeCookie("__next_hmr_refresh_hash_", "value")).toBe(false);
		});

		it("retorna falso para nome de cookie ou valor vazio", () => {
			expect(service.shouldIncludeCookie("", "value")).toBe(false);
			expect(service.shouldIncludeCookie("name", "")).toBe(false);
			expect(service.shouldIncludeCookie("", "")).toBe(false);
		});

		it("retorna falso para valor contendo apenas espaços", () => {
			expect(service.shouldIncludeCookie("name", "   ")).toBe(false);
		});

		it("retorna verdadeiro para cookie válido", () => {
			expect(service.shouldIncludeCookie("name", "value")).toBe(true);
		});
	});

	describe("buildCookieHeader", () => {
		it("constrói um cabeçalho apenas com cookies válidos", () => {
			const cookies = {
				valid1: "value1",
				__next_hmr_refresh_hash__: "shouldBeExcluded",
				valid2: "value2",
			};
			expect(service.buildCookieHeader(cookies)).toBe("valid1=value1; valid2=value2");
		});

		it("sanitiza e codifica valores de cookie quando necessário", () => {
			const cookies = {
				a: "val;ue",
				b: "simple",
				c: "välue",
			};
			expect(service.buildCookieHeader(cookies)).toBe(`a=${encodeURIComponent("val;ue")}; b=simple; c=${encodeURIComponent("välue")}`);
		});

		it("retorna string vazia se todos os cookies forem excluídos", () => {
			const cookies = {
				__next_hmr_refresh_hash__: "value",
				"": "",
			};
			expect(service.buildCookieHeader(cookies)).toBe("");
		});
	});

	describe("processCookieData", () => {
		it("retorna null se success for false", () => {
			expect(service.processCookieData({ success: false, value: { a: "b" }, message: "" })).toBeNull();
		});

		it("retorna null se value estiver ausente", () => {
			expect(service.processCookieData({ success: true, value: null, message: "" })).toBeNull();
		});

		it("retorna null se não houver cookies válidos", () => {
			expect(service.processCookieData({ success: true, value: { __next_hmr_refresh_hash__: "value" }, message: "" })).toBeNull();
		});

		it("retorna string de cabeçalho de cookie se válido", () => {
			expect(service.processCookieData({ success: true, value: { foo: "bar" }, message: "" })).toBe("foo=bar");
		});
	});
});
