import { ChartAreaInteractive } from "../components/charts/chart-area-interactive";
import { DataTable } from "../components/data-table/data-table";
import { SectionCards } from "../components/section-cards";
import data from "../constants/mock-table.json";

export const DashboardAdminScreen = () => {
	return (
		<>
			<SectionCards />
			<div className="px-4 lg:px-6">
				<ChartAreaInteractive />
			</div>
			<DataTable data={data} />
		</>
	);
};
