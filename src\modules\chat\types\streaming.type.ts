import { IChatError } from "./handlers.type";

export interface IStreamingOptions {
	onChunk?: (chunk: IChatStreamResponse) => void;
	onChunkData?: (chunkData: IChatStreamResponse) => void;
	onError?: (error: IChatError) => void;
	signal?: AbortSignal;
}

export interface IChatStreamRequest {
	message: string;
	sessionId?: string;
}

export interface IChatStreamResponse {
	content?: string;
	type?: "complete" | "session";
}
