import { IInspectionCollabBySector } from "@/modules/inspection/types/collaborator-by-sector/find-all.dto";
import { ColumnDef } from "@tanstack/react-table";
import { CollabBySectorActions } from "./actions";

export const inspectionCollabBySectorColumns: ColumnDef<IInspectionCollabBySector>[] = [
	{
		accessorKey: "pin",
		header: () => <div className="pl-[10px] text-start font-semibold">Pin</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-[10px]">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.pin}</span>
			</div>
		),
	},
	{
		accessorKey: "collaboratorName",
		header: () => <div className="text-center font-semibold">Colaborador</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.collaboratorName}</span>
			</div>
		),
	},
	{
		accessorKey: "sectorName",
		header: () => <div className="text-center font-semibold">Setor</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.sectorName}</span>
			</div>
		),
	},
	{
		accessorKey: "actions",
		header: () => <div className="pr-[10px] text-end font-semibold">Ações</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-end pr-[10px]">
				<CollabBySectorActions id={String(row.original.id)} name={row.original.sectorName} />
			</div>
		),
	},
];
