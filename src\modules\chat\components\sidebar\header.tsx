import { useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import { <PERSON><PERSON>, Trash2, X } from "lucide-react";
import { Button } from "../../../../shared/components/shadcn/button";
import { toggleChatStateAtom } from "../../atoms/controls/trigger.atom";
import { isMessagesAvailableAtom } from "../../atoms/session/info.atom";
import { removeSessionAtom } from "../../atoms/session/remove.atom";

export const ChatHeader = () => {
	const hasMessages = useAtomValue(isMessagesAvailableAtom);
	const onClose = useSetAtom(toggleChatStateAtom);
	const removeSession = useSetAtom(removeSession<PERSON>tom);

	return (
		<header
			className={`relative flex items-center justify-between overflow-hidden border-b border-slate-200 bg-gradient-to-r from-slate-50 via-white to-slate-50 px-6 py-4 shadow-sm backdrop-blur-md transition-all duration-300 ease-in-out dark:border-slate-700 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900`}
		>
			<div className="from-primary/5 to-primary/5 pointer-events-none absolute inset-0 bg-gradient-to-r via-transparent" />
			<div className="relative z-10 flex items-center gap-3">
				<div className="bg-primary/10 dark:bg-primary/20 border-primary/20 dark:border-primary/30 rounded-controls flex h-10 w-10 items-center justify-center border shadow-sm transition-all duration-200 hover:scale-105">
					<Bot className="text-primary h-5 w-5" />
				</div>
				<div className="flex flex-col">
					<h2 className="text-lg leading-tight font-semibold text-slate-800 dark:text-slate-100">Doorinha</h2>
					<span className="text-xs font-medium text-slate-500 dark:text-slate-400">Assistente Virtual</span>
				</div>
			</div>
			<div className="relative z-10 flex items-center gap-1">
				{hasMessages && (
					<Button
						onClick={removeSession}
						variant="ghost"
						size="sm"
						className="group rounded-controls h-9 w-9 p-0 text-slate-500 transition-all duration-200 hover:bg-red-50 hover:text-red-600 dark:text-slate-400 dark:hover:bg-red-950/30 dark:hover:text-red-400"
						title="Limpar conversa"
					>
						<Trash2 className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
					</Button>
				)}
				<Button
					onClick={onClose}
					variant="ghost"
					size="sm"
					className="group rounded-controls h-9 w-9 p-0 text-slate-500 transition-all duration-200 hover:bg-slate-100 hover:text-slate-700 dark:text-slate-400 dark:hover:bg-slate-700/50 dark:hover:text-slate-200"
					title="Fechar chat"
				>
					<X className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
				</Button>
			</div>
			<div className="via-primary/30 absolute right-0 bottom-0 left-0 h-px bg-gradient-to-r from-transparent to-transparent" />
		</header>
	);
};
