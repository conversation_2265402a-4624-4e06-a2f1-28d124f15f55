"use client";

import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { 
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/shared/components/shadcn/dropdown-menu";
import { cn } from "@/shared/lib/shadcn/utils";
import { useTheme } from "../hooks/use-theme.hook";
import { IThemeToggleProps } from "../types/theme.types";

/**
 * Componente para alternar entre temas
 * Suporta modo simples (toggle) e modo completo (dropdown)
 */
export function ThemeToggle({
	size = "md",
	showLabel = false,
	className,
	onToggle,
}: IThemeToggleProps) {
	const { currentTheme, setTheme, isLoading } = useTheme();

	const handleThemeChange = async (themeId: string) => {
		try {
			await setTheme(themeId);
			onToggle?.(themeId);
		} catch (error) {
			console.error("Erro ao alterar tema:", error);
		}
	};

	const getIcon = () => {
		switch (currentTheme) {
			case "light":
				return <Sun className="h-4 w-4" />;
			case "dark":
				return <Moon className="h-4 w-4" />;
			case "system":
				return <Monitor className="h-4 w-4" />;
			default:
				return <Sun className="h-4 w-4" />;
		}
	};

	const getLabel = () => {
		switch (currentTheme) {
			case "light":
				return "Claro";
			case "dark":
				return "Escuro";
			case "system":
				return "Sistema";
			default:
				return "Tema";
		}
	};

	const sizeClasses = {
		sm: "h-8 w-8",
		md: "h-9 w-9",
		lg: "h-10 w-10",
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="outline"
					size={size === "sm" ? "sm" : size === "lg" ? "lg" : "default"}
					className={cn(
						"relative",
						!showLabel && sizeClasses[size],
						className
					)}
					disabled={isLoading}
				>
					{getIcon()}
					{showLabel && <span className="ml-2">{getLabel()}</span>}
					<span className="sr-only">Alternar tema</span>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="min-w-[160px]">
				<DropdownMenuItem
					onClick={() => handleThemeChange("light")}
					className="cursor-pointer"
				>
					<Sun className="mr-2 h-4 w-4" />
					<span>Claro</span>
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => handleThemeChange("dark")}
					className="cursor-pointer"
				>
					<Moon className="mr-2 h-4 w-4" />
					<span>Escuro</span>
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => handleThemeChange("system")}
					className="cursor-pointer"
				>
					<Monitor className="mr-2 h-4 w-4" />
					<span>Sistema</span>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

/**
 * Toggle simples entre light/dark
 */
export function SimpleThemeToggle({
	size = "md",
	className,
	onToggle,
}: Omit<IThemeToggleProps, "showLabel">) {
	const { currentTheme, setTheme, isLoading } = useTheme();

	const handleToggle = async () => {
		try {
			const newTheme = currentTheme === "light" ? "dark" : "light";
			await setTheme(newTheme);
			onToggle?.(newTheme);
		} catch (error) {
			console.error("Erro ao alternar tema:", error);
		}
	};

	const sizeClasses = {
		sm: "h-8 w-8",
		md: "h-9 w-9",
		lg: "h-10 w-10",
	};

	return (
		<Button
			variant="outline"
			size={size === "sm" ? "sm" : size === "lg" ? "lg" : "default"}
			className={cn(sizeClasses[size], className)}
			onClick={handleToggle}
			disabled={isLoading}
		>
			{currentTheme === "light" ? (
				<Sun className="h-4 w-4" />
			) : (
				<Moon className="h-4 w-4" />
			)}
			<span className="sr-only">
				Alternar para tema {currentTheme === "light" ? "escuro" : "claro"}
			</span>
		</Button>
	);
}

/**
 * Toggle com animação suave
 */
export function AnimatedThemeToggle({
	size = "md",
	className,
	onToggle,
}: Omit<IThemeToggleProps, "showLabel">) {
	const { currentTheme, setTheme, isLoading } = useTheme();

	const handleToggle = async () => {
		try {
			const newTheme = currentTheme === "light" ? "dark" : "light";
			await setTheme(newTheme);
			onToggle?.(newTheme);
		} catch (error) {
			console.error("Erro ao alternar tema:", error);
		}
	};

	const sizeClasses = {
		sm: "h-8 w-8",
		md: "h-9 w-9",
		lg: "h-10 w-10",
	};

	return (
		<Button
			variant="outline"
			size={size === "sm" ? "sm" : size === "lg" ? "lg" : "default"}
			className={cn(
				sizeClasses[size],
				"relative overflow-hidden transition-all duration-200",
				className
			)}
			onClick={handleToggle}
			disabled={isLoading}
		>
			<div className="relative">
				<Sun 
					className={cn(
						"h-4 w-4 transition-all duration-200",
						currentTheme === "light" 
							? "rotate-0 scale-100" 
							: "rotate-90 scale-0"
					)} 
				/>
				<Moon 
					className={cn(
						"absolute inset-0 h-4 w-4 transition-all duration-200",
						currentTheme === "dark" 
							? "rotate-0 scale-100" 
							: "-rotate-90 scale-0"
					)} 
				/>
			</div>
			<span className="sr-only">
				Alternar para tema {currentTheme === "light" ? "escuro" : "claro"}
			</span>
		</Button>
	);
}

/**
 * Toggle com indicador de sistema
 */
export function SystemAwareThemeToggle({
	size = "md",
	className,
	onToggle,
}: Omit<IThemeToggleProps, "showLabel">) {
	const { currentTheme, systemPreference, setTheme, isLoading } = useTheme();

	const handleToggle = async () => {
		try {
			let newTheme: string;
			
			if (currentTheme === "system") {
				// Se está em system, vai para o oposto da preferência
				newTheme = systemPreference === "dark" ? "light" : "dark";
			} else if (currentTheme === "light") {
				newTheme = "dark";
			} else {
				newTheme = "system";
			}
			
			await setTheme(newTheme);
			onToggle?.(newTheme);
		} catch (error) {
			console.error("Erro ao alternar tema:", error);
		}
	};

	const getIcon = () => {
		if (currentTheme === "system") {
			return <Monitor className="h-4 w-4" />;
		}
		return currentTheme === "light" ? (
			<Sun className="h-4 w-4" />
		) : (
			<Moon className="h-4 w-4" />
		);
	};

	const sizeClasses = {
		sm: "h-8 w-8",
		md: "h-9 w-9",
		lg: "h-10 w-10",
	};

	return (
		<Button
			variant="outline"
			size={size === "sm" ? "sm" : size === "lg" ? "lg" : "default"}
			className={cn(
				sizeClasses[size],
				"relative",
				currentTheme === "system" && "ring-2 ring-primary/20",
				className
			)}
			onClick={handleToggle}
			disabled={isLoading}
		>
			{getIcon()}
			{currentTheme === "system" && (
				<div className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-primary" />
			)}
			<span className="sr-only">
				Alternar tema (atual: {currentTheme})
			</span>
		</Button>
	);
}
