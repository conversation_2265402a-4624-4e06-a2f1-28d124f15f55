"use client";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { useCallback, useState } from "react";

export const useModalForEditingForms = () => {
	const editModal = useModal();
	const [selectedId, setSelectedId] = useState<string | null>(null);

	const openEditModal = useCallback(
		(id: string) => {
			setSelectedId(id);
			editModal.openModal();
		},
		[editModal],
	);

	const closeEditModal = useCallback(() => {
		editModal.closeModal();
		setSelectedId(null);
	}, [editModal]);

	return {
		isOpen: editModal.isOpen,
		selectedId,
		openEditModal,
		closeEditModal,
	};
};
