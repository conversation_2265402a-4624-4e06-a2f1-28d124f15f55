"use client";

import { Check, Palette, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/shared/components/shadcn/select";
import { Badge } from "@/shared/components/shadcn/badge";
import { cn } from "@/shared/lib/shadcn/utils";
import { useTheme, useCustomThemes } from "../hooks/use-theme.hook";
import { IThemeSelectorProps } from "../types/theme.types";

/**
 * Seletor completo de temas com suporte a temas customizados
 */
export function ThemeSelector({
	variant = "dropdown",
	size = "md",
	showCustomThemes = true,
	showSystemOption = true,
	className,
	onThemeChange,
}: IThemeSelectorProps) {
	const { currentTheme, availableThemes, setTheme, isLoading } = useTheme();
	const { customThemes } = useCustomThemes();

	const handleThemeChange = async (themeId: string) => {
		try {
			await setTheme(themeId);
			onThemeChange?.(themeId);
		} catch (error) {
			console.error("Erro ao alterar tema:", error);
		}
	};

	const standardThemes = availableThemes.filter(theme => !theme.isCustom);
	const displayCustomThemes = showCustomThemes ? customThemes : [];

	if (variant === "dropdown") {
		return (
			<Select
				value={currentTheme}
				onValueChange={handleThemeChange}
				disabled={isLoading}
			>
				<SelectTrigger className={cn("w-[180px]", className)}>
					<SelectValue placeholder="Selecionar tema" />
				</SelectTrigger>
				<SelectContent>
					{standardThemes.map((theme) => {
						if (theme.id === "system" && !showSystemOption) return null;
						
						return (
							<SelectItem key={theme.id} value={theme.id}>
								<div className="flex items-center gap-2">
									<div
										className="h-3 w-3 rounded-full border"
										style={{
											backgroundColor: theme.colors.primary,
										}}
									/>
									{theme.name}
								</div>
							</SelectItem>
						);
					})}
					
					{displayCustomThemes.length > 0 && (
						<>
							<div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
								Temas Customizados
							</div>
							{displayCustomThemes.map((theme) => (
								<SelectItem key={theme.id} value={theme.id}>
									<div className="flex items-center gap-2">
										<div
											className="h-3 w-3 rounded-full border"
											style={{
												backgroundColor: theme.colors.primary,
											}}
										/>
										{theme.name}
										<Badge variant="secondary" className="text-xs">
											Custom
										</Badge>
									</div>
								</SelectItem>
							))}
						</>
					)}
				</SelectContent>
			</Select>
		);
	}

	if (variant === "grid") {
		return (
			<div className={cn("grid gap-2", className)}>
				<div className="grid grid-cols-2 gap-2">
					{standardThemes.map((theme) => {
						if (theme.id === "system" && !showSystemOption) return null;
						
						const isSelected = currentTheme === theme.id;
						
						return (
							<Button
								key={theme.id}
								variant={isSelected ? "default" : "outline"}
								size={size}
								className="relative justify-start"
								onClick={() => handleThemeChange(theme.id)}
								disabled={isLoading}
							>
								<div
									className="mr-2 h-3 w-3 rounded-full border"
									style={{
										backgroundColor: theme.colors.primary,
									}}
								/>
								{theme.name}
								{isSelected && (
									<Check className="ml-auto h-4 w-4" />
								)}
							</Button>
						);
					})}
				</div>
				
				{displayCustomThemes.length > 0 && (
					<div className="space-y-2">
						<div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
							<Palette className="h-4 w-4" />
							Temas Customizados
						</div>
						<div className="grid grid-cols-1 gap-2">
							{displayCustomThemes.map((theme) => {
								const isSelected = currentTheme === theme.id;
								
								return (
									<Button
										key={theme.id}
										variant={isSelected ? "default" : "outline"}
										size={size}
										className="relative justify-start"
										onClick={() => handleThemeChange(theme.id)}
										disabled={isLoading}
									>
										<div
											className="mr-2 h-3 w-3 rounded-full border"
											style={{
												backgroundColor: theme.colors.primary,
											}}
										/>
										{theme.name}
										<Badge variant="secondary" className="ml-2 text-xs">
											Custom
										</Badge>
										{isSelected && (
											<Check className="ml-auto h-4 w-4" />
										)}
									</Button>
								);
							})}
						</div>
					</div>
				)}
			</div>
		);
	}

	// Variant "toggle" - apenas temas padrão
	return (
		<div className={cn("flex items-center gap-1 rounded-md border p-1", className)}>
			{standardThemes.map((theme) => {
				if (theme.id === "system" && !showSystemOption) return null;
				
				const isSelected = currentTheme === theme.id;
				
				return (
					<Button
						key={theme.id}
						variant={isSelected ? "default" : "ghost"}
						size="sm"
						className="h-8 px-3"
						onClick={() => handleThemeChange(theme.id)}
						disabled={isLoading}
					>
						<div
							className="mr-2 h-2 w-2 rounded-full"
							style={{
								backgroundColor: theme.colors.primary,
							}}
						/>
						{theme.name}
					</Button>
				);
			})}
		</div>
	);
}

/**
 * Seletor simples apenas para temas padrão
 */
export function SimpleThemeSelector({
	className,
	onThemeChange,
}: Pick<IThemeSelectorProps, "className" | "onThemeChange">) {
	return (
		<ThemeSelector
			variant="toggle"
			showCustomThemes={false}
			showSystemOption={true}
			className={className}
			onThemeChange={onThemeChange}
		/>
	);
}

/**
 * Seletor compacto para espaços pequenos
 */
export function CompactThemeSelector({
	className,
	onThemeChange,
}: Pick<IThemeSelectorProps, "className" | "onThemeChange">) {
	const { currentTheme, setTheme, isLoading } = useTheme();

	const themes = [
		{ id: "light", name: "L", color: "#ffffff" },
		{ id: "dark", name: "D", color: "#000000" },
		{ id: "system", name: "S", color: "#6b7280" },
	];

	const handleThemeChange = async (themeId: string) => {
		try {
			await setTheme(themeId);
			onThemeChange?.(themeId);
		} catch (error) {
			console.error("Erro ao alterar tema:", error);
		}
	};

	return (
		<div className={cn("flex items-center gap-1", className)}>
			{themes.map((theme) => {
				const isSelected = currentTheme === theme.id;
				
				return (
					<Button
						key={theme.id}
						variant={isSelected ? "default" : "outline"}
						size="sm"
						className="h-6 w-6 p-0"
						onClick={() => handleThemeChange(theme.id)}
						disabled={isLoading}
					>
						<span className="text-xs font-medium">{theme.name}</span>
					</Button>
				);
			})}
		</div>
	);
}

/**
 * Seletor com preview das cores
 */
export function ColorPreviewThemeSelector({
	className,
	onThemeChange,
}: Pick<IThemeSelectorProps, "className" | "onThemeChange">) {
	const { currentTheme, availableThemes, setTheme, isLoading } = useTheme();

	const handleThemeChange = async (themeId: string) => {
		try {
			await setTheme(themeId);
			onThemeChange?.(themeId);
		} catch (error) {
			console.error("Erro ao alterar tema:", error);
		}
	};

	return (
		<div className={cn("grid grid-cols-3 gap-3", className)}>
			{availableThemes.filter(theme => !theme.isCustom).map((theme) => {
				const isSelected = currentTheme === theme.id;
				
				return (
					<Button
						key={theme.id}
						variant="outline"
						className={cn(
							"h-16 flex-col gap-2 p-2",
							isSelected && "ring-2 ring-primary"
						)}
						onClick={() => handleThemeChange(theme.id)}
						disabled={isLoading}
					>
						<div className="flex gap-1">
							<div
								className="h-3 w-3 rounded-full border"
								style={{ backgroundColor: theme.colors.primary }}
							/>
							<div
								className="h-3 w-3 rounded-full border"
								style={{ backgroundColor: theme.colors.background }}
							/>
							<div
								className="h-3 w-3 rounded-full border"
								style={{ backgroundColor: theme.colors.accent }}
							/>
						</div>
						<span className="text-xs font-medium">{theme.name}</span>
						{isSelected && (
							<Check className="absolute top-1 right-1 h-3 w-3" />
						)}
					</Button>
				);
			})}
		</div>
	);
}
