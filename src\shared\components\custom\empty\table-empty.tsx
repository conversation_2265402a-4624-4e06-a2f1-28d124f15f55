import { motion, type Transition } from "framer-motion";
import { cn } from "../../../lib/shadcn/utils";

interface ActionButton {
	label: string;
	onClick: () => void;
	variant?: "primary" | "secondary" | "outline";
	icon?: React.ReactNode;
}

interface EmptyStateProps {
	searchTerm?: string;
	icon: React.ReactNode;
	title: string;
	description: string;
	tip?: string;
	actions?: ActionButton[];
	size?: "sm" | "md" | "lg";
	variant?: "default" | "search" | "error" | "info";
	className?: string;
	children?: React.ReactNode;
	showDefaultSearchTip?: boolean;
}

const sizeClasses = {
	sm: {
		container: "px-4 py-6",
		iconContainer: "p-2 mb-3",
		title: "text-base",
		description: "text-xs",
		tip: "text-xs px-2 py-1",
	},
	md: {
		container: "px-6 py-8",
		iconContainer: "p-3 mb-4",
		title: "text-lg",
		description: "text-sm",
		tip: "text-xs px-3 py-2",
	},
	lg: {
		container: "px-8 py-12",
		iconContainer: "p-4 mb-6",
		title: "text-xl",
		description: "text-base",
		tip: "text-sm px-4 py-3",
	},
};

const variantClasses = {
	default: {
		iconBg: "bg-muted/50",
		tipBg: "bg-muted/30",
	},
	search: {
		iconBg: "bg-blue-50 dark:bg-blue-950/50",
		tipBg: "bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800",
	},
	error: {
		iconBg: "bg-red-50 dark:bg-red-950/50",
		tipBg: "bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800",
	},
	info: {
		iconBg: "bg-amber-50 dark:bg-amber-950/50",
		tipBg: "bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800",
	},
};

const buttonVariants = {
	primary: "bg-primary text-primary-foreground hover:bg-primary/90",
	secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
	outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
};

const springSmooth: Transition = { type: "spring", stiffness: 90, damping: 16 };
const easeSmooth: Transition = { duration: 0.45, ease: [0.22, 1, 0.36, 1] };
const hoverScale = 1.02;
const tapScale = 0.98;

export const EmptyStateTable: React.FC<EmptyStateProps> = ({
	searchTerm,
	icon,
	title,
	description,
	tip,
	actions = [],
	size = "md",
	variant = "default",
	className,
	children,
	showDefaultSearchTip = true,
}) => {
	const isSearching = searchTerm && searchTerm.trim().length > 0;
	const shouldShowSearchTip = isSearching && showDefaultSearchTip;
	const defaultSearchTip = "💡 Dica: Verifique a grafia ou tente termos mais gerais";

	const currentVariant = isSearching && variant === "default" ? "search" : variant;
	const sizeConfig = sizeClasses[size];
	const variantConfig = variantClasses[currentVariant];

	const displayTip = tip || (shouldShowSearchTip ? defaultSearchTip : null);

	return (
		<div className={cn("flex flex-col items-center justify-center space-y-4 text-center", sizeConfig.container, className)}>
			<motion.div
				initial={{ scale: 0.96, opacity: 0 }}
				animate={{ scale: 1, opacity: 1 }}
				transition={springSmooth}
				className="flex flex-col items-center"
				layout
			>
				<motion.div
					whileHover={{ scale: hoverScale }}
					whileTap={{ scale: tapScale }}
					transition={{ type: "spring", stiffness: 160, damping: 20 }}
					className={cn(
						"rounded-controls shadow-sm ring-1 transition-colors duration-200",
						sizeConfig.iconContainer,
						variantConfig.iconBg,
						"ring-border/50",
					)}
				>
					<motion.div initial={{ rotate: -6, opacity: 0 }} animate={{ rotate: 0, opacity: 1 }} transition={easeSmooth}>
						{icon}
					</motion.div>
				</motion.div>

				<div className="mt-2 space-y-1">
					<motion.h3
						className={cn("text-foreground font-semibold", sizeConfig.title)}
						initial={{ y: 6, opacity: 0 }}
						animate={{ y: 0, opacity: 1 }}
						transition={{ ...easeSmooth, delay: 0.05 }}
					>
						{title}
					</motion.h3>
					<motion.p
						className={cn("text-muted-foreground", sizeConfig.description)}
						initial={{ y: 6, opacity: 0 }}
						animate={{ y: 0, opacity: 1 }}
						transition={{ ...easeSmooth, delay: 0.12 }}
					>
						{description}
					</motion.p>
					{isSearching && (
						<motion.div
							className="text-muted-foreground mt-2 text-xs"
							initial={{ y: 6, opacity: 0 }}
							animate={{ y: 0, opacity: 1 }}
							transition={{ ...easeSmooth, delay: 0.16 }}
						>
							Resultado da busca por: <span className="font-medium">{`"${searchTerm}"`}</span>
						</motion.div>
					)}
				</div>

				{children && (
					<motion.div className="mt-4" initial={{ y: 8, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ ...easeSmooth, delay: 0.18 }}>
						{children}
					</motion.div>
				)}

				{actions.length > 0 && (
					<motion.div
						className="mt-6 flex flex-wrap justify-center gap-2"
						initial={{ y: 10, opacity: 0 }}
						animate={{ y: 0, opacity: 1 }}
						transition={{ ...easeSmooth, delay: 0.22 }}
						layout
					>
						{actions.map((action, index) => (
							<button
								key={index}
								onClick={action.onClick}
								className={cn(
									"focus:ring-ring rounded-controls inline-flex items-center gap-2 px-4 py-2 text-sm font-medium transition-colors duration-200 focus:ring-2 focus:ring-offset-2 focus:outline-none",
									buttonVariants[action.variant || "outline"],
								)}
							>
								{action.icon}
								{action.label}
							</button>
						))}
					</motion.div>
				)}

				{displayTip && (
					<motion.div
						className={cn("rounded-controls mt-4 transition-colors duration-200", sizeConfig.tip, variantConfig.tipBg, "text-muted-foreground")}
						initial={{ y: 10, opacity: 0 }}
						animate={{ y: 0, opacity: 1 }}
						transition={{ ...easeSmooth, delay: 0.26 }}
						layout
					>
						{displayTip}
					</motion.div>
				)}
			</motion.div>
		</div>
	);
};
