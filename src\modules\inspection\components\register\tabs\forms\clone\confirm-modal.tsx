import { useCloneFormMutation } from "@/modules/inspection/hooks/form/clone/mutation.hook";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/shadcn/button";
import { Copy } from "lucide-react";

interface ConfirmCloneFormModalProps {
	formId: string;
	isOpen: boolean;
	title?: string;
	onClose: () => void;
}

export const ConfirmCloneFormModal: React.FC<ConfirmCloneFormModalProps> = ({ formId, isOpen, title, onClose }) => {
	const { cloneForm } = useCloneFormMutation();

	const handleConfirm = () => {
		cloneForm(formId);
		onClose();
	};

	return (
		<Modal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
			<div className="flex flex-col items-center space-y-4 p-2 text-center">
				<Copy className="text-primary h-12 w-12" />
				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Clonar Formulário</h2>
					<p className="text-muted-foreground">
						Você realmente deseja copiar o formulário: <span className="text-primary font-medium">{title}</span>
					</p>
				</div>
				<div className="flex gap-3 pt-2">
					<Button variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button onClick={handleConfirm}>Confirmar</Button>
				</div>
			</div>
		</Modal>
	);
};
