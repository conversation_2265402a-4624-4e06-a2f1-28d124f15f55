"use client";

import { LoadingContainer } from "@/layout/components/container/loading-container";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import { ReactNode } from "react";
import { useAuthCheck } from "../hooks/auth/auth-check.hook";
import { useUserSync } from "../hooks/user/use-user-sync.hook";

interface IUserProviderProps extends IReactChildrenType {
	fallback?: ReactNode;
}

export function UserProvider({ children, fallback }: Readonly<IUserProviderProps>) {
	const { hasToken, isChecking } = useAuthCheck();
	const { isLoading } = useUserSync();
	const shouldShowLoading = isChecking || (hasToken && isLoading);
	if (shouldShowLoading && fallback) return <>{fallback}</>;
	return <>{children}</>;
}

export function UserProviderWithLoading({ children, fallback }: Readonly<IUserProviderProps>) {
	return <UserProvider fallback={fallback ?? <LoadingContainer />}>{children}</UserProvider>;
}
