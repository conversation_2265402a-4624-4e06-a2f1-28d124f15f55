"use client";

import { LoadingSpinner } from "@/shared/components/custom/loading/loading-spinner";
import { cn } from "@/shared/lib/shadcn/utils";
import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { createPortal } from "react-dom";
import { logoutLoadingStateAtom } from "../atoms/logout-loading.atom";

interface IHandleLogoutLoadingOverlayProps {
	className?: string;
}

export const LogoutLoadingOverlay: React.FC<IHandleLogoutLoadingOverlayProps> = ({ className }) => {
	const { isLoading, message } = useAtomValue(logoutLoadingStateAtom);
	if (typeof window === "undefined") return null;

	return createPortal(
		<AnimatePresence mode="wait">
			{isLoading && (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
					transition={{
						duration: 0.15,
						ease: "easeInOut",
					}}
					className={cn(
						"fixed inset-0 z-[9999] flex items-center justify-center",
						"bg-background/25 backdrop-blur-md",
						"pointer-events-auto",
						className,
					)}
					role="dialog"
					aria-modal="true"
					aria-labelledby="logout-loading-title"
					aria-describedby="logout-loading-description"
				>
					<motion.div
						initial={{ scale: 0.9, opacity: 0 }}
						animate={{ scale: 1, opacity: 1 }}
						exit={{ scale: 0.9, opacity: 0 }}
						transition={{
							duration: 0.2,
							ease: "easeOut",
							delay: isLoading ? 0.05 : 0,
						}}
						className={cn("rounded-main flex flex-col items-center space-y-6", "bg-card border p-8 shadow-2xl", "mx-4 w-full max-w-sm")}
					>
						<div className="relative">
							<LoadingSpinner size="lg" className="text-primary" />
							<div className="absolute inset-0 animate-ping">
								<div className="border-primary/10 h-8 w-8 rounded-full border-2" />
							</div>
						</div>
						<div className="space-y-2 text-center">
							<h3 id="logout-loading-title" className="text-foreground text-lg font-semibold">
								{message}
							</h3>
							<p id="logout-loading-description" className="text-muted-foreground text-sm">
								Por favor, aguarde enquanto processamos sua solicitação...
							</p>
						</div>
						<div className="w-full">
							<div className="bg-muted h-1 overflow-hidden rounded-full">
								<motion.div
									className="bg-primary h-full rounded-full"
									initial={{ width: "0%" }}
									animate={{ width: "100%" }}
									transition={{
										duration: 2,
										ease: "easeInOut",
										repeat: Infinity,
									}}
								/>
							</div>
						</div>
					</motion.div>
				</motion.div>
			)}
		</AnimatePresence>,
		document.body,
	);
};
