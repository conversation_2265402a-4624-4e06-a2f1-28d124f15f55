import { ICreateRequest } from "@/shared/types/requests/create-request.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { createRequest } from "../index";

type HelperRequestParams<T> = Omit<ICreateRequest<T>, "method">;

const isClientSide = (): boolean => typeof window !== "undefined";
const executeOnServer = async <TSuccess>(serverFunction: string, ...args: unknown[]): Promise<ApiResponse<TSuccess>> => {
	try {
		const serverModule = await import("../server/server-request-executor");
		const functionMap = {
			executeServerGetRequest: serverModule.executeServerGetRequest,
			executeServerPostRequest: serverModule.executeServerPostRequest,
			executeServerPutRequest: serverModule.executeServerPutRequest,
			executeServerPatchRequest: serverModule.executeServerPatchRequest,
			executeServerDeleteRequest: serverModule.executeServerDeleteRequest,
			executeServerRequest: serverModule.executeServerRequest,
		};

		const fn = functionMap[serverFunction as keyof typeof functionMap];
		if (!fn) {
			throw new Error(`Função ${serverFunction} não encontrada`);
		}

		return await (fn as (...args: unknown[]) => Promise<ApiResponse<TSuccess>>)(...args);
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao executar requisição no servidor",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		} as ApiResponse<TSuccess>;
	}
};

const normalizeResponse = <T>(response: ApiResponse<T>): ApiResponse<T> => ({
	success: response.success,
	data: JSON.parse(JSON.stringify(response.data)),
	status: response.status,
});

export const createGetRequest = async <TSuccess>(path: string, options?: Omit<HelperRequestParams<never>, "path" | "body">): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return executeOnServer("executeServerGetRequest", path, options);
	const response = await createRequest<TSuccess>({ ...options, path, method: "GET" });
	return normalizeResponse(response);
};

export const createPostRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return executeOnServer("executeServerPostRequest", path, body, options);
	const response = await createRequest<TSuccess, TRequest>({ ...options, path, method: "POST", body });
	return normalizeResponse(response);
};

export const createPutRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return executeOnServer("executeServerPutRequest", path, body, options);
	const response = await createRequest<TSuccess, TRequest>({ ...options, path, method: "PUT", body });
	return normalizeResponse(response);
};

export const createPatchRequest = async <TSuccess, TRequest = unknown>(
	path: string,
	body?: TRequest,
	options?: Omit<HelperRequestParams<TRequest>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return executeOnServer("executeServerPatchRequest", path, body, options);
	const response = await createRequest<TSuccess, TRequest>({ ...options, path, method: "PATCH", body });
	return normalizeResponse(response);
};

export const createDeleteRequest = async <TSuccess>(
	path: string,
	options?: Omit<HelperRequestParams<never>, "path" | "body">,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) return executeOnServer("executeServerDeleteRequest", path, options);
	const response = await createRequest<TSuccess>({ ...options, path, method: "DELETE" });
	return normalizeResponse(response);
};

export const createBatchRequestsRequest = async <T extends readonly unknown[]>(requests: { [K in keyof T]: () => Promise<T[K]> }): Promise<{
	[K in keyof T]: T[K] | Error;
}> => {
	const results = await Promise.allSettled(requests.map(request => request()));
	return results.map(result => (result.status === "fulfilled" ? result.value : result.reason)) as { [K in keyof T]: T[K] | Error };
};

export const createWithTimeoutRequest = async <TSuccess, TRequest = unknown>(
	params: ICreateRequest<TRequest>,
	timeoutMs: number,
): Promise<ApiResponse<TSuccess>> => {
	if (isClientSide()) {
		return executeOnServer("executeServerRequest", {
			method: params.method,
			path: params.path,
			body: params.body,
			options: { ...params, timeout: timeoutMs },
		});
	}

	const response = await createRequest<TSuccess, TRequest>({ ...params, timeout: timeoutMs });
	return normalizeResponse(response);
};
