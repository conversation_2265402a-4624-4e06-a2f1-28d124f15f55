import { IChatStreamResponse } from "../types/streaming.type";

export const processBuffer = (buffer: string) => {
	const lines = buffer.split("\n");
	const remainingBuffer = lines.pop() || "";
	const processedLines = lines.filter(line => line.trim());
	return { processedLines, remainingBuffer };
};

export const processStreamLine = (line: string): IChatStreamResponse | undefined => {
	const trimmed = line.trim();
	if (!trimmed) return undefined;
	let jsonStr = trimmed;
	if (trimmed.startsWith("data:")) jsonStr = trimmed.slice(5).trim();
	if (jsonStr === "[DONE]" || jsonStr.startsWith(":")) return undefined;

	try {
		const parsed = JSON.parse(jsonStr);
		const content = parsed?.content ?? parsed?.delta?.content ?? parsed?.choices?.[0]?.delta?.content ?? parsed?.text;
		const hasContent = content !== undefined && content !== null;
		if (hasContent) return { content: String(content), type: parsed?.type };
		if (parsed?.type) return { content: "", type: parsed.type };
		return undefined;
	} catch {
		if (jsonStr && !jsonStr.startsWith("{") && !jsonStr.startsWith("[")) return { content: jsonStr, type: undefined };
		return undefined;
	}
};
