import { toast } from "@/core/toast";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { SECTOR_ENDPOINTS } from "../../api/endpoints";
import { sectorQueryKeys } from "../../constants/query";
import { SECTOR_SUBJECTS } from "../../constants/subjects";
import { TCreateSector } from "../../validators/create";

export const useCreateSectorMutation = () => {
	const queryClient = useQueryClient();
	const { canCreate } = usePermissions();

	const createSectorMutation = useMutation({
		mutationKey: sectorQueryKeys.custom("create"),
		mutationFn: async (sector: TCreateSector) => {
			if (!canCreate(SECTOR_SUBJECTS.SECTOR)) throw new Error("Sem permissão para criar setor");
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(SECTOR_ENDPOINTS.CREATE, sector);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => sectorQueryKeys.invalidateAll(queryClient),
	});

	return {
		createCell: (form: TCreateSector) =>
			toast.promise(createSectorMutation.mutateAsync(form), {
				loading: "Criando célula...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
