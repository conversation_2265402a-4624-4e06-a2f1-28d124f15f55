import type { FC, JSX } from "react";

type LoadingSpinnerProps = Readonly<{
	size?: number;
	className?: string;
}>;

export const LoadingSpinner: FC<LoadingSpinnerProps> = ({ size = 64, className = "" }): JSX.Element => (
	<div
		className={`border-primary animate-spin rounded-full border-4 border-dashed ${className}`}
		style={{ width: size, height: size }}
		aria-label="Loading"
		role="status"
	/>
);

type LoadingContainerProps = Readonly<{
	spinnerSize?: number;
	className?: string;
}>;

export const LoadingContainer: FC<LoadingContainerProps> = ({ spinnerSize, className = "" }) => (
	<div className={`from-primary/15 to-background flex h-screen flex-col items-center justify-center bg-gradient-to-br ${className}`}>
		<LoadingSpinner size={spinnerSize} />
		<p className="text-muted-foreground animate-fade-in mt-5 text-sm font-medium">Carregando dados, por favor aguarde...</p>
	</div>
);
