"use client";
import { useClickOutside } from "@/shared/hooks/utils/click-outside.hook";
import { cn } from "@/shared/lib/shadcn/utils";
import type { IHandleModalComponentProps } from "@/shared/types/components/react-children.type";
import React, { useCallback, useEffect } from "react";
import { createPortal } from "react-dom";

const MODAL_SIZES = {
	sm: "max-w-sm",
	md: "max-w-md",
	lg: "max-w-lg",
	xl: "max-w-2xl",
};

export const Modal: React.FC<IHandleModalComponentProps> = ({
	isOpen,
	onClose,
	title,
	description,
	children,
	showCloseButton = true,
	closeOnOverlayClick = true,
	size = "md",
	className,
	overlayClassName,
	disableEscapeKeyDown = false,
	initialFocusRef,
}) => {
	const modalRef = useClickOutside<HTMLDivElement>(() => {}, { enabled: false });

	useEffect(() => {
		if (isOpen && initialFocusRef?.current) {
			initialFocusRef.current.focus();
		}
	}, [isOpen, initialFocusRef]);

	const handleKeyDown = useCallback(
		(e: KeyboardEvent) => {
			if (!disableEscapeKeyDown && e.key === "Escape" && isOpen) {
				onClose();
			}
		},
		[disableEscapeKeyDown, isOpen, onClose],
	);

	useEffect(() => {
		if (!isOpen) return;
		document.addEventListener("keydown", handleKeyDown);
		document.body.style.overflow = "hidden";
		return () => {
			document.removeEventListener("keydown", handleKeyDown);
			document.body.style.overflow = "";
		};
	}, [isOpen, handleKeyDown]);

	if (typeof window === "undefined") return null;
	if (!isOpen) return null;

	return createPortal(
		<div
			aria-modal="true"
			role="dialog"
			tabIndex={-1}
			className={cn("fixed inset-0 z-20 flex items-center justify-center px-2 sm:px-4", overlayClassName)}
			style={{ background: "rgba(0,0,0,0.5)" }}
			onClick={e => {
				if (e.target === e.currentTarget && closeOnOverlayClick) {
					e.stopPropagation();
					onClose();
				}
			}}
		>
			<div
				ref={modalRef}
				className={cn(
					"rounded-main relative flex max-h-[90vh] w-full flex-col bg-white shadow-sm transition-all duration-300 outline-none focus:outline-none dark:bg-gray-900",
					MODAL_SIZES[size],
					className,
				)}
				tabIndex={-1}
				aria-labelledby={title ? "modal-title" : undefined}
				aria-describedby={description ? "modal-desc" : undefined}
				onClick={e => e.stopPropagation()}
			>
				{(title || showCloseButton) && (
					<div className="flex items-center justify-between border-b border-gray-200 px-6 py-4 dark:border-gray-700">
						{title && (
							<h2 id="modal-title" className="text-lg font-semibold text-gray-900 dark:text-white">
								{title}
							</h2>
						)}
						{showCloseButton && (
							<button
								type="button"
								aria-label="Fechar modal"
								onClick={onClose}
								className="focus:ring-primary ml-4 cursor-pointer rounded-full p-2 hover:bg-gray-100 focus:ring-2 focus:ring-offset-2 focus:outline-none dark:hover:bg-gray-800"
							>
								<svg className="h-5 w-5 text-gray-700 dark:text-gray-200" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
								</svg>
							</button>
						)}
					</div>
				)}
				{description && (
					<div id="modal-desc" className="px-6 pt-2 pb-0 text-sm text-gray-600 dark:text-gray-300">
						{description}
					</div>
				)}
				<div className="flex-1 overflow-y-auto px-6 py-4">{children}</div>
			</div>
		</div>,
		document.body,
	);
};
