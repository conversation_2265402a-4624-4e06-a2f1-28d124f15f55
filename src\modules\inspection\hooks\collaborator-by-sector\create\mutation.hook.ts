import { toast } from "@/core/toast";
import { COLLAB_BY_SECTOR_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { ICreateCollabBySectorDTO } from "@/modules/inspection/types/collaborator-by-sector/create.dto";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useCreateCollabBySectorMutation() {
	const queryClient = useQueryClient();
	const { canCreate } = usePermissions();

	const createFormMutation = useMutation({
		mutationKey: inspectionKeys.collabBysector.custom("create"),
		mutationFn: async (formData: ICreateCollabBySectorDTO) => {
			if (!canCreate(INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR)) throw new Error("Você não tem permissão para criar este vínculo.");
			const res = await createPostRequest<IMessageGlobalReturn>(COLLAB_BY_SECTOR_ENDPOINTS.CREATE, formData);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => inspectionKeys.collabBysector.invalidateAllLists(queryClient),
	});
	return {
		createCollabBySectorType: (formData: ICreateCollabBySectorDTO) =>
			toast.promise(createFormMutation.mutateAsync(formData), {
				loading: "Criando vinculo de colaborador por setor...",
				success: data => data.message ?? "Vinculo criado com sucesso!",
				error: error => error.message || "Erro ao vinculo",
			}),
	};
}
