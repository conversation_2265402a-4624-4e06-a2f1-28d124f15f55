"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { editKnowledgeSchema, TEditKnowledgeSchema } from "../../validators/edit-knowledge";

export const useEditKnowledgeForm = (defaultValues?: Partial<TEditKnowledgeSchema>) => {
	return useForm<TEditKnowledgeSchema>({
		resolver: zodResolver(editKnowledgeSchema),
		defaultValues: {
			title: "",
			content: "",
			isActive: true,
			...defaultValues,
		},
		mode: "onChange",
	});
};
