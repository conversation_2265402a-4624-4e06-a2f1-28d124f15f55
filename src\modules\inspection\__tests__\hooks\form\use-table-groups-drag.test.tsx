jest.mock("jotai", () => {
	const actual = jest.requireActual("jotai");
	return {
		...actual,
		useAtomValue: jest.fn(() => [{ tempId: "g1" }, { tempId: "g2" }]),
		useSetAtom: jest.fn(() => jest.fn()),
	};
});

import { useTableGroupsDrag } from "@/modules/inspection/hooks/form/create/table-groups-drag.hook";
import { renderHook } from "@testing-library/react";

describe("useTableGroupsDrag", () => {
	it("should expose handlers and dataGroupsId", () => {
		const { result } = renderHook(() => useTableGroupsDrag());

		expect(result.current.handleDragEnd).toBeDefined();
		expect(Array.isArray(result.current.dataGroupsId)).toBe(true);
		expect(typeof result.current.sortableId).toBe("string");
	});
});
