"use client";

import { getAllCookies } from "../../../../shared/lib/cookies/crud/get";
import { CookieHeaderService } from "../../../../shared/lib/cookies/services/cookie-header.service";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { IChatStreamRequest, IStreamingOptions } from "../../types/streaming.type";
import { StreamErrorHandler } from "./error-handler.service";
import { StreamProcessor } from "./streaming-processor";

interface IStreamingConfig {
	readonly chunkTimeout: number;
	readonly maxRetries: number;
	readonly retryDelay: number;
}

type IStreamingHeaders = Record<string, string> & {
	readonly "Content-Type": string;
	readonly Accept: string;
	readonly "Cache-Control": string;
	readonly Connection: string;
	readonly Cookie?: string;
};

export class StreamRequestHandler {
	private readonly config: IStreamingConfig = {
		chunkTimeout: 30000,
		maxRetries: 3,
		retryDelay: 1000,
	};

	constructor(
		private readonly processor: StreamProcessor,
		private readonly errorHandler: StreamErrorHandler,
		private readonly cookieService: CookieHeaderService = new CookieHeaderService(),
	) {}

	public async handle(request: IChatStreamRequest, options: IStreamingOptions, signal: AbortSignal): Promise<void> {
		for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
			try {
				await this.executeStreamingRequest(request, options, signal);
				return;
			} catch (error) {
				if (signal.aborted) return;
				const message = this.extractErrorMessage(error);
				if (message.includes("Erro HTTP 401")) {
					options.onError?.(this.errorHandler.create(error, message));
					throw error;
				}
				if (this.isLastAttempt(attempt)) {
					const originalMessage = this.extractErrorMessage(error);
					const fullMessage = `Falha após ${this.config.maxRetries} tentativas - ${originalMessage}`;
					options.onError?.(this.errorHandler.create(error, fullMessage));
					throw error;
				}

				await this.delayRetry(attempt);
			}
		}
	}

	private async executeStreamingRequest(request: IChatStreamRequest, options: IStreamingOptions, signal: AbortSignal): Promise<void> {
		const timeoutId = this.setupTimeout(signal);

		try {
			const headers = await this.buildRequestHeaders();
			const response = await this.makeHttpRequest(request, headers, signal);
			await this.validateAndProcessResponse(response, options);
		} finally {
			clearTimeout(timeoutId);
		}
	}

	private setupTimeout(signal: AbortSignal): NodeJS.Timeout {
		return setTimeout(() => signal?.throwIfAborted?.(), this.config.chunkTimeout);
	}

	private async buildRequestHeaders(): Promise<IStreamingHeaders> {
		const baseHeaders: IStreamingHeaders = {
			"Content-Type": "application/json",
			Accept: "text/event-stream",
			"Cache-Control": "no-cache",
			Connection: "keep-alive",
		};

		try {
			const cookiesData = await getAllCookies();
			const cookieHeader = this.cookieService.processCookieData(cookiesData);
			if (cookieHeader?.trim()) return { ...baseHeaders, Cookie: cookieHeader };
		} catch (error) {
			console.warn("Erro ao obter cookies para streaming:", error);
		}

		return baseHeaders;
	}

	private async makeHttpRequest(request: IChatStreamRequest, headers: IStreamingHeaders, signal: AbortSignal): Promise<Response> {
		return fetch(CHAT_ENDPOINTS.ASK, {
			method: "POST",
			headers,
			body: JSON.stringify(request),
			signal,
			mode: "cors",
		});
	}

	private async validateAndProcessResponse(response: Response, options: IStreamingOptions): Promise<void> {
		if (!response.ok) this.errorHandler.throwHttp(response);
		if (!response.body) throw new Error("Nenhuma resposta recebida do servidor");
		await this.processor.process(response.body, options);
	}

	private isLastAttempt(attempt: number): boolean {
		return attempt === this.config.maxRetries - 1;
	}

	private extractErrorMessage(error: unknown): string {
		if (!error) return "Erro desconhecido";
		if (error instanceof Error) return error.message;
		try {
			return typeof error === "string" ? error : JSON.stringify(error);
		} catch {
			return "Erro (não foi possível serializar o erro)";
		}
	}

	private async delayRetry(attempt: number): Promise<void> {
		const delay = this.config.retryDelay * (attempt + 1);
		await new Promise(resolve => setTimeout(resolve, delay));
	}
}
