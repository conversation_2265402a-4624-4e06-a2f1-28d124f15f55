import { ColumnDef } from "@tanstack/react-table";
import { IFormLinkDto } from "../../../../../types/form-link/dtos/find-all.dto";
import { CellComponentActions } from "./actions";

export const inspectionFormLinksColumns: ColumnDef<IFormLinkDto>[] = [
	{
		accessorKey: "form",
		header: () => <div className="pl-[10px] text-start font-semibold">Formulário</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-[10px]">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.form}</span>
			</div>
		),
	},
	{
		accessorKey: "cell",
		header: () => <div className="text-center font-semibold">Célula</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.cell}</span>
			</div>
		),
	},
	{
		accessorKey: "activity",
		header: () => <div className="text-center font-semibold">Atividade</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.activity}</span>
			</div>
		),
	},
	{
		accessorKey: "linkedAt",
		header: () => <div className="text-center font-semibold">Vinculado em</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.linkedAt}</span>
			</div>
		),
	},
	{
		accessorKey: "delete",
		header: () => <div className="pr-[10px] text-end font-semibold">Ações</div>,
		cell: ({ row }) => (
			<CellComponentActions
				linkId={String(row.original.id)}
				name={`
				${row.original.form} - ${row.original.cell} - ${row.original.activity}
			`}
			/>
		),
	},
];
