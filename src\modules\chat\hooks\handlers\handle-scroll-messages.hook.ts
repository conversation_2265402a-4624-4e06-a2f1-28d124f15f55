"use client";
import { useCallback, useEffect, useRef, useState } from "react";
import { IChatMessage } from "../../types/messages.type";

interface IScrollMessages {
	messages: IChatMessage[];
	isOpen: boolean;
}

const NEAR_BOTTOM_THRESHOLD = 80;

export const useScrollMessages = ({ messages, isOpen }: IScrollMessages) => {
	const scrollAreaRef = useRef<HTMLDivElement | null>(null);
	const [isAtBottom, setIsAtBottom] = useState(true);
	const [autoScroll, setAutoScroll] = useState(true);
	const [hasNewMessages, setHasNewMessages] = useState(false);

	const computeIsAtBottom = useCallback((element: HTMLDivElement | null, threshold = 24) => {
		if (!element) return true;
		const distance = element.scrollHeight - element.scrollTop - element.clientHeight;
		return distance <= threshold;
	}, []);

	const scrollToBottom = useCallback((behavior: ScrollBehavior = "auto") => {
		const element = scrollAreaRef.current;
		if (!element) return;
		element.scrollTo({ top: element.scrollHeight, behavior });
	}, []);

	useEffect(() => {
		if (!isOpen) return;
		requestAnimationFrame(() => {
			scrollToBottom("auto");
			const el = scrollAreaRef.current;
			const atBottom = computeIsAtBottom(el);
			setIsAtBottom(atBottom);
			setAutoScroll(true);
			setHasNewMessages(false);
		});
	}, [isOpen, computeIsAtBottom, scrollToBottom]);

	useEffect(() => {
		const el = scrollAreaRef.current;
		if (!el) return;

		if (autoScroll) {
			scrollToBottom(isOpen ? "smooth" : "auto");
			setHasNewMessages(false);
			setIsAtBottom(true);
		} else {
			const near = computeIsAtBottom(el, NEAR_BOTTOM_THRESHOLD);
			setHasNewMessages(!near);
			setIsAtBottom(near);
		}
	}, [messages, autoScroll, computeIsAtBottom, isOpen, scrollToBottom]);

	const handleScroll: React.UIEventHandler<HTMLDivElement> = useCallback(
		e => {
			const el = e.currentTarget;
			const near = computeIsAtBottom(el, NEAR_BOTTOM_THRESHOLD);
			setIsAtBottom(near);
			if (near) {
				setAutoScroll(true);
				setHasNewMessages(false);
			} else {
				setAutoScroll(false);
			}
		},
		[computeIsAtBottom],
	);

	const handleGoToLast = useCallback(() => {
		setAutoScroll(true);
		setHasNewMessages(false);
		scrollToBottom("smooth");
	}, [scrollToBottom]);

	return {
		scrollAreaRef,
		handleScroll,
		handleGoToLast,
		isAtBottom,
		autoScroll,
		hasNewMessages,
		setAutoScroll,
		setIsAtBottom,
		setHasNewMessages,
		scrollToBottom,
		computeIsAtBottom,
	};
};
