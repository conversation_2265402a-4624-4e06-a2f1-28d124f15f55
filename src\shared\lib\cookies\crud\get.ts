"use server";
import { cookies } from "next/headers";
import { ICookieGetAllResult } from "../types";

// export const getCookie = async ({ name }: IGetCookieProps): Promise<ICookieGetResult> => {
// 	try {
// 		const cookieStore = await cookies();
// 		const cookie = cookieStore.get(name);
// 		if (!cookie) return { success: false, message: `O cookie ${name} não foi encontrado`, value: null };
// 		return { success: true, message: `O cookie ${name} foi encontrado`, value: cookie.value };
// 	} catch (error: unknown) {
// 		const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
// 		return { success: false, message: `Erro ao buscar cookie: ${errorMessage}`, value: null };
// 	}
// };

export const getAllCookies = async (): Promise<ICookieGetAllResult> => {
	try {
		const cookieStore = await cookies();
		const allCookies = cookieStore.getAll();

		if (allCookies.length === 0) {
			return {
				success: false,
				message: "Nenhum cookie foi encontrado",
				value: null,
			};
		}

		const cookiesObject = allCookies.reduce((acc, cookie) => {
			acc[cookie.name] = cookie.value;
			return acc;
		}, {} as Record<string, string>);

		return {
			success: true,
			message: `${allCookies.length} cookie(s) encontrado(s)`,
			value: cookiesObject,
		};
	} catch (error: unknown) {
		const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
		return {
			success: false,
			message: `Erro ao buscar todos os cookies: ${errorMessage}`,
			value: null,
		};
	}
};
