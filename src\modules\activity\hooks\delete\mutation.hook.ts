import { SUBJECTS } from "@/config/permissions";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../core/toast";
import { createDeleteRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";

export const useDeleteActivityMutation = () => {
	const queryClient = useQueryClient();
	const { canDelete } = usePermissions();

	const deleteMutation = useMutation({
		mutationKey: activityQueryKeys.custom("delete"),
		mutationFn: async (id: string) => {
			if (!canDelete(SUBJECTS.ACTIVITY)) throw new Error("Você não tem permissão para excluir atividades");
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(ACTIVITY_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => activityQueryKeys.invalidateAll(queryClient),
	});

	return {
		deleteActivity: (id: string) =>
			toast.promise(deleteMutation.mutateAsync(id), {
				loading: "Excluindo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
