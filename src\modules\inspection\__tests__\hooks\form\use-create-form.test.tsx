import { useCreateForm } from "@/modules/inspection/hooks/form/create/form.hook";
import { act, renderHook } from "@testing-library/react";

describe("useCreateForm", () => {
	it("deve expor métodos do react-hook-form e resetForm", () => {
		const { result } = renderHook(() => useCreateForm());

		expect(result.current.methods).toBeDefined();
		expect(typeof result.current.resetForm).toBe("function");

		act(() => result.current.methods.setValue("title", "novo"));
		expect(result.current.methods.getValues("title")).toBe("novo");

		act(() => result.current.resetForm());
		expect(result.current.methods.getValues("title")).toBe("");
	});
});
