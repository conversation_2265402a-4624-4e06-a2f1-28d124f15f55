import { useAddKnowledgeForm } from "@/modules/chat/hooks/create/form.hook";
import { useAddKnowledgeMutation } from "@/modules/chat/hooks/create/mutation.hook";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { Save, X } from "lucide-react";

interface AddKnowledgeModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const AddKnowledgeModal = ({ isOpen, onClose }: AddKnowledgeModalProps) => {
	const form = useAddKnowledgeForm();

	const handleClose = () => {
		form.reset();
		onClose();
	};

	const { addKnowledge, isPending } = useAddKnowledgeMutation(handleClose);

	return (
		<Modal
			isOpen={isOpen}
			onClose={handleClose}
			title="Adicionar Novo Conhecimento"
			description="Crie um novo conhecimento para o chatbot de IA"
			size="xl"
			className="max-h-[90vh]"
		>
			<div className="space-y-6">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(data => addKnowledge(data))} className="space-y-4">
						<FormField
							control={form.control}
							name="title"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Título *</FormLabel>
									<FormControl>
										<Input autoFocus placeholder="Digite o título do conhecimento..." {...field} className="focus:border-primary/50" />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="content"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Conteúdo *</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Digite o conteúdo do conhecimento..."
											rows={8}
											{...field}
											className="focus:border-primary/50 resize-none"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<div className="flex justify-end gap-3">
							<Button type="button" variant="ghost" onClick={handleClose}>
								<X className="size-4" />
								Cancelar
							</Button>
							<Button type="submit" disabled={isPending} className="flex items-center gap-2">
								<Save className="size-4" />
								{isPending ? "Salvando..." : "Salvar"}
							</Button>
						</div>
					</form>
				</Form>
			</div>
		</Modal>
	);
};
