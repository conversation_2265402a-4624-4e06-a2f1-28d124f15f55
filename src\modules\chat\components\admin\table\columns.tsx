import { IChatKnowledgeDto } from "@/modules/chat/types/dtos/find-all-knowledge.dto";
import { ColumnDef } from "@tanstack/react-table";
import { ChatKnowledgeActions } from "./actions";

export const chatAdminColumns: ColumnDef<IChatKnowledgeDto>[] = [
	{
		accessorKey: "title",
		header: () => <div className="pl-[10px] text-start font-semibold">T<PERSON><PERSON><PERSON></div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-[10px]">
				<div className="max-w-[300px]">
					<span className="text-primary block truncate font-medium" title={row.original.title}>
						{row.original.title}
					</span>
				</div>
			</div>
		),
		size: 300,
	},
	{
		accessorKey: "isActive",
		header: () => <div className="text-center font-semibold">Status</div>,
		cell: ({ row }) => (
			<div className="flex justify-center">
				<span className="text-muted-foreground text-sm font-medium">{row.original.isActive ? "Ativo" : "Inativo"}</span>
			</div>
		),
		size: 120,
	},
	{
		accessorKey: "createdAt",
		header: () => <div className="text-center font-semibold">Criado em</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center">
				<span className="text-sm text-gray-600">
					{new Date(row.original.createdAt).toLocaleDateString("pt-BR", {
						day: "2-digit",
						month: "2-digit",
						year: "numeric",
						hour: "2-digit",
						minute: "2-digit",
					})}
				</span>
			</div>
		),
		size: 180,
	},
	{
		id: "actions",
		header: () => <div className="pr-[10px] text-right font-semibold">Ações</div>,
		cell: ({ row }) => <ChatKnowledgeActions knowledgeId={String(row.original.id)} title={row.original.title} />,
		size: 80,
	},
];
