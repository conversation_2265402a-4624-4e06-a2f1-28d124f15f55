import { createCookie } from "@/shared/lib/cookies/crud/create";
import { removeCookie } from "@/shared/lib/cookies/crud/remove";
import { AxiosInstance, AxiosResponse } from "axios";

interface CookieOptions {
	maxAge: number;
	expires?: Date;
	path?: string;
	secure?: boolean;
	sameSite?: "strict" | "lax" | "none";
	httpOnly?: boolean;
}

interface ParsedCookie {
	name: string;
	value: string;
	shouldRemove: boolean;
	options: CookieOptions;
}

export const cookieResponseInterceptor = (instance: AxiosInstance): void => {
	instance.interceptors.response.use(
		async (response: AxiosResponse) => {
			try {
				await processCookiesFromResponse(response);
			} catch (error) {
				console.error("Erro ao processar cookies da resposta:", error);
			}
			return response;
		},
		async error => {
			if (error.response) {
				try {
					await processCookiesFromResponse(error.response);
				} catch (cookieError) {
					console.error("Erro ao processar cookies da resposta de erro:", cookieError);
				}
			}
			return Promise.reject(error);
		}
	);
};

async function processCookiesFromResponse(response: AxiosResponse): Promise<void> {
	const setCookieHeaders = response.headers["set-cookie"];
	if (!setCookieHeaders || !Array.isArray(setCookieHeaders)) return;
	await Promise.all(
		setCookieHeaders.map(header =>
			processSingleCookie(header).catch(error => console.error("Erro ao processar cookie individual:", header, error))
		)
	);
}

async function processSingleCookie(cookieHeader: string): Promise<void> {
	const cookieData = parseCookieHeader(cookieHeader);
	if (!cookieData) return;
	const { name, value, shouldRemove, options } = cookieData;
	if (shouldRemove) {
		await removeCookie({ name });
		return;
	}
	await createCookie({ name, value, options });
}

function parseCookieHeader(cookieHeader: string): ParsedCookie | null {
	try {
		const [nameValuePair, ...directives] = cookieHeader.split(";").map(part => part.trim());
		const [name, value = ""] = nameValuePair.split("=");

		if (!name) return null;

		const options: CookieOptions = {
			maxAge: 86400,
		};

		const shouldRemove = isCookieToBeRemoved(value, directives);
		parseCookieDirectives(directives, options);

		return {
			name: name.trim(),
			value: value.trim(),
			shouldRemove,
			options,
		};
	} catch (error) {
		console.error("Erro ao fazer parse do cookie header:", cookieHeader, error);
		return null;
	}
}

function isCookieToBeRemoved(value: string, directives: string[]): boolean {
	if (value === "" || value.toLowerCase() === "deleted") return true;
	const normalized = directives.map(d => d.trim());
	const maxAgeDirective = normalized.find(d => d.toLowerCase().startsWith("max-age="));
	if (maxAgeDirective) {
		const parts = maxAgeDirective.split("=");
		const raw = parts[1] ? parts[1].trim() : "";
		const maxAge = raw === "" ? NaN : parseInt(raw, 10);
		if (!Number.isNaN(maxAge) && maxAge <= 0) return true;
	}

	const expiresDirective = normalized.find(d => d.toLowerCase().startsWith("expires="));
	if (expiresDirective) {
		const idx = expiresDirective.indexOf("=");
		const dateStr = idx >= 0 ? expiresDirective.slice(idx + 1).trim() : "";
		const expires = new Date(dateStr);
		if (!Number.isNaN(expires.getTime()) && expires.getTime() <= Date.now()) return true;
	}

	return false;
}

function createDirectiveHandlers(options: CookieOptions): Record<string, (val?: string) => void> {
	return {
		"max-age": (val?: string) => {
			if (val) options.maxAge = parseInt(val, 10);
		},
		expires: (val?: string) => {
			if (val) options.expires = new Date(val);
		},
		path: (val?: string) => {
			if (val) options.path = val;
		},
		secure: () => {
			options.secure = true;
		},
		httponly: () => {
			options.httpOnly = true;
		},
		samesite: (val?: string) => {
			if (!val) return;
			const v = val.toLowerCase();
			if (["strict", "lax", "none"].includes(v)) {
				options.sameSite = v as "strict" | "lax" | "none";
			}
		},
	};
}

function parseSingleDirective(directive: string, handlers: Record<string, (val?: string) => void>): void {
	const [rawKey, rawVal] = directive.split("=");
	if (!rawKey) return;
	const key = rawKey.trim().toLowerCase();
	const val = rawVal ? rawVal.trim() : undefined;
	const handler = handlers[key];
	if (handler) handler(val);
}

function parseCookieDirectives(directives: string[], options: CookieOptions): void {
	const handlers = createDirectiveHandlers(options);
	directives.forEach(directive => parseSingleDirective(directive, handlers));
}
