import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { cn } from "@/shared/lib/shadcn/utils";
import { motion } from "framer-motion";

interface TableLoadingProps {
	rows?: number;
	columns?: number;
}

export const TableLoading: React.FC<TableLoadingProps> = ({ rows = 5, columns = 4 }) => {
	const widths = ["w-32", "w-40", "w-28", "w-24"];

	return (
		<>
			{Array.from({ length: rows }).map((_, rowIndex) => (
				<TableRow key={rowIndex} className={cn("transition-colors duration-200", "hover:bg-transparent")}>
					{Array.from({ length: columns }).map((_, colIndex) => {
						const isFirst = colIndex === 0;
						const isLast = colIndex === columns - 1;
						const widthClass = widths[colIndex % widths?.length];

						return (
							<TableCell key={colIndex} className="h-[24px]">
								<div className="bg-muted/70 rounded-controls relative overflow-hidden">
									<div className={cn(isFirst ? "h-5" : "h-4", isFirst ? "w-36" : isLast ? "w-24" : widthClass)} />
									<motion.span
										aria-hidden
										initial={{ x: "-100%" }}
										animate={{ x: "100%" }}
										transition={{ duration: 1.6, repeat: Infinity, ease: "linear" }}
										className="via-foreground/10 pointer-events-none absolute inset-0 block bg-gradient-to-r from-transparent to-transparent"
										style={{
											filter: "blur(0.5px)",
										}}
									/>
								</div>
							</TableCell>
						);
					})}
				</TableRow>
			))}
		</>
	);
};
