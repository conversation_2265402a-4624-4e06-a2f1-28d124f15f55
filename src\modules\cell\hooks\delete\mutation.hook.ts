import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../core/toast";
import { createDeleteRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { CELL_ENDPOINTS } from "../../api/endpoints";
import { cellQueryKeys } from "../../constants/query";
import { CELL_SUBJECTS } from "../../constants/subjects";

export const useDeleteCellMutation = () => {
	const queryClient = useQueryClient();
	const { canDelete } = usePermissions();

	const deleteMutation = useMutation({
		mutationKey: cellQueryKeys.custom("delete"),
		mutationFn: async (id: string) => {
			if (!canDelete(CELL_SUBJECTS.CELL)) throw new Error("Você não tem permissão para excluir uma célula.");
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(CELL_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => cellQueryKeys.invalidateAll(queryClient),
	});

	return {
		deleteCell: (id: string) =>
			toast.promise(deleteMutation.mutateAsync(id), {
				loading: "Excluindo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
