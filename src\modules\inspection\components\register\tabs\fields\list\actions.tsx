import { But<PERSON> } from "@/shared/components/shadcn/button";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Trash } from "lucide-react";
import { ConfirmDeleteFieldsModal } from "../delete/confirm-delete-modal";

export function FieldsListActions({ fieldsId, name }: { fieldsId: string; name: string }) {
	const deleteModal = useModal();
	return (
		<div className="pr-[10px] text-right">
			<Button size="icon" onClick={deleteModal.openModal} className="group ml-2 h-8 w-8 bg-red-500/5 hover:bg-red-500/30">
				<Trash className="h-4 w-4 text-red-500 group-hover:text-white" />
			</Button>
			<ConfirmDeleteFieldsModal isOpen={deleteModal.isOpen} onClose={deleteModal.closeModal} name={name} fieldsId={fieldsId} />
		</div>
	);
}
