import { useCreateMeasureMutation } from "@/modules/inspection/hooks/measures/create/mutation.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";

jest.mock("@/shared/lib/requests", () => ({
	createPostRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canCreate: () => true })),
}));


const mockedCreatePostRequest = createPostRequest as jest.MockedFunction<typeof createPostRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Medida criada com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Error ao excluir medida" };

describe("useCreateMeasure", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve criar a medida com sucesso", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useCreateMeasureMutation(), { wrapper });
		await expect(
			result.current.createMeasure({
				abbreviation: "ABV",
				name: "Abreviação",
			}),
		).resolves.toEqual(mockSuccess);
	});

	it("deve lançar erro ao falhar", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useCreateMeasureMutation(), { wrapper });
		await expect(
			result.current.createMeasure({
				abbreviation: "ABV",
				name: "Abreviação",
			}),
		).rejects.toThrow("Error ao excluir medida");
	});
});
