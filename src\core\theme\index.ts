/**
 * Sistema de <PERSON> - S.I.M³P
 * Exportações principais para uso em toda a aplicação
 */

// Types
export type {
	CustomThemeId,
	IThemeChangeEvent,
	IThemeColors,
	IThemeConfig,
	IThemePersistence,
	IThemePersistenceConfig,
	IThemeSelectorProps,
	IThemeService,
	IThemeState,
	IThemeToggleProps,
	IThemeValidation,
	IUseThemeHook,
	ThemeColorKey,
	ThemeConfigKey,
	ThemeId,
	ThemeMode,
	ThemeStateKey,
} from "./types/theme.types";

// Constants
export { THEME_CONSTANTS } from "./types/theme.types";

export {
	CORPORATE_BLUE_THEME,
	CSS_VARIABLE_MAP,
	DARK_THEME,
	DEFAULT_THEMES,
	EXAMPLE_CUSTOM_THEMES,
	LIGHT_THEME,
	SYSTEM_THEME,
	THEME_CSS_CLASSES,
	THEME_PERSISTENCE_CONFIG,
	THEME_TRANSITION_CSS,
} from "./constants/themes.constants";

// Atoms
export {
	addCustomThemeAtom,
	applyThemeAtom,
	availableThemesAtom,
	clearThemeErrorAtom,
	currentThemeAtom,
	currentThemeConfigAtom,
	customThemesAtom,
	removeCustomThemeAtom,
	systemPreferenceAtom,
	themeErrorAtom,
	themeLoadingAtom,
	themeStateAtom,
	toggleThemeAtom,
	updateCustomThemeAtom,
} from "./atoms/theme.atom";

export {
	autoSyncThemeAtom,
	clearThemePersistenceAtom,
	initializeThemeAtom,
	loadClientThemeAtom,
	loadServerThemeAtom,
	manualSyncThemeAtom,
	persistThemeAtom,
	themeConflictAtom,
	themeHydratedAtom,
	themePersistenceErrorAtom,
	themePersistenceStatusAtom,
	themeSyncingAtom,
} from "./atoms/theme-persistence.atom";

// Services
export { ThemeService, themeService } from "./services/theme.service";

export { ThemePersistenceService, themePersistenceService } from "./services/theme-persistence.service";

// Providers
export { SimpleThemeProvider, SSRThemeProvider, ThemeProvider, withThemeHydration } from "./providers/theme.provider";

// Hooks
export { useCustomThemes, useSystemTheme, useTheme, useThemeDebug, useThemePersistence, useThemeToggle, useThemeValidation } from "./hooks/use-theme.hook";

// Components
export { AnimatedThemeToggle, SimpleThemeToggle, SystemAwareThemeToggle, ThemeToggle } from "./components/theme-toggle";

export { ColorPreviewThemeSelector, CompactThemeSelector, SimpleThemeSelector, ThemeSelector } from "./components/theme-selector";

// Utilities
export const createCustomTheme = (config: Omit<IThemeConfig, "id" | "isCustom">) => {
	return {
		...config,
		id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
		isCustom: true,
	} as IThemeConfig;
};

export const validateThemeColors = (colors: Partial<IThemeColors>): boolean => {
	const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
	const oklchRegex = /^oklch\([^)]+\)$/;

	return Object.values(colors).every(color => {
		if (typeof color !== "string") return false;
		return hexColorRegex.test(color) || oklchRegex.test(color) || color.startsWith("rgb") || color.startsWith("hsl");
	});
};

export const getThemeFromCSSVariables = (): Partial<IThemeColors> => {
	if (typeof window === "undefined") return {};

	const style = getComputedStyle(document.documentElement);
	const colors: Partial<IThemeColors> = {};

	Object.entries(CSS_VARIABLE_MAP).forEach(([key, cssVar]) => {
		const value = style.getPropertyValue(cssVar).trim();
		if (value) {
			(colors as any)[key] = value;
		}
	});

	return colors;
};

export const applyThemeToElement = (element: HTMLElement, themeConfig: IThemeConfig): void => {
	Object.entries(themeConfig.colors).forEach(([key, value]) => {
		const cssVar = CSS_VARIABLE_MAP[key as keyof IThemeColors];
		if (cssVar) {
			element.style.setProperty(cssVar, value);
		}
	});
};

import { CSS_VARIABLE_MAP } from "./constants/themes.constants";
// Re-export types for convenience
import type { IThemeColors, IThemeConfig } from "./types/theme.types";
