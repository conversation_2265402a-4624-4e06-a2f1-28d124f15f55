/**
 * Sistema de <PERSON> - S.I.M³P
 * Exportações principais para uso em toda a aplicação
 */

// Types
export type {
	ThemeMode,
	CustomThemeId,
	ThemeId,
	IThemeColors,
	IThemeConfig,
	IThemeState,
	IThemePersistence,
	IThemePersistenceConfig,
	IThemeService,
	IUseThemeHook,
	IThemeChangeEvent,
	IThemeValidation,
	IThemeSelectorProps,
	IThemeToggleProps,
	ThemeColorKey,
	ThemeConfigKey,
	ThemeStateKey,
} from "./types/theme.types";

// Constants
export {
	THEME_CONSTANTS,
} from "./types/theme.types";

export {
	LIGHT_THEME,
	DARK_THEME,
	SYSTEM_THEME,
	CORPORATE_BLUE_THEME,
	DEFAULT_THEMES,
	EXAMPLE_CUSTOM_THEMES,
	THEME_PERSISTENCE_CONFIG,
	CSS_VARIABLE_MAP,
	THEME_CSS_CLASSES,
	THEME_TRANSITION_CSS,
} from "./constants/themes.constants";

// Atoms
export {
	systemPreferenceAtom,
	currentThemeAtom,
	availableThemesAtom,
	customThemesAtom,
	themeLoadingAtom,
	themeErrorAtom,
	currentThemeConfigAtom,
	themeStateAtom,
	applyThemeAtom,
	toggleThemeAtom,
	addCustomThemeAtom,
	updateCustomThemeAtom,
	removeCustomThemeAtom,
	clearThemeErrorAtom,
} from "./atoms/theme.atom";

export {
	themeHydratedAtom,
	themeSyncingAtom,
	themePersistenceErrorAtom,
	initializeThemeAtom,
	persistThemeAtom,
	loadServerThemeAtom,
	loadClientThemeAtom,
	clearThemePersistenceAtom,
	themePersistenceStatusAtom,
	autoSyncThemeAtom,
	manualSyncThemeAtom,
	themeConflictAtom,
} from "./atoms/theme-persistence.atom";

// Services
export {
	ThemeService,
	themeService,
} from "./services/theme.service";

export {
	ThemePersistenceService,
	themePersistenceService,
} from "./services/theme-persistence.service";

// Providers
export {
	ThemeProvider,
	SimpleThemeProvider,
	SSRThemeProvider,
	withThemeHydration,
} from "./providers/theme.provider";

// Hooks
export {
	useTheme,
	useThemeToggle,
	useCustomThemes,
	useSystemTheme,
	useThemePersistence,
	useThemeValidation,
	useThemeDebug,
} from "./hooks/use-theme.hook";

// Components
export {
	ThemeToggle,
	SimpleThemeToggle,
	AnimatedThemeToggle,
	SystemAwareThemeToggle,
} from "./components/theme-toggle";

export {
	ThemeSelector,
	SimpleThemeSelector,
	CompactThemeSelector,
	ColorPreviewThemeSelector,
} from "./components/theme-selector";

// Utilities
export const createCustomTheme = (config: Omit<IThemeConfig, "id" | "isCustom">) => {
	return {
		...config,
		id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
		isCustom: true,
	} as IThemeConfig;
};

export const validateThemeColors = (colors: Partial<IThemeColors>): boolean => {
	const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
	const oklchRegex = /^oklch\([^)]+\)$/;
	
	return Object.values(colors).every(color => {
		if (typeof color !== "string") return false;
		return hexColorRegex.test(color) || 
		       oklchRegex.test(color) || 
		       color.startsWith("rgb") || 
		       color.startsWith("hsl");
	});
};

export const getThemeFromCSSVariables = (): Partial<IThemeColors> => {
	if (typeof window === "undefined") return {};
	
	const style = getComputedStyle(document.documentElement);
	const colors: Partial<IThemeColors> = {};
	
	Object.entries(CSS_VARIABLE_MAP).forEach(([key, cssVar]) => {
		const value = style.getPropertyValue(cssVar).trim();
		if (value) {
			(colors as any)[key] = value;
		}
	});
	
	return colors;
};

export const applyThemeToElement = (element: HTMLElement, themeConfig: IThemeConfig): void => {
	Object.entries(themeConfig.colors).forEach(([key, value]) => {
		const cssVar = CSS_VARIABLE_MAP[key as keyof IThemeColors];
		if (cssVar) {
			element.style.setProperty(cssVar, value);
		}
	});
};

// Re-export types for convenience
import type { IThemeConfig, IThemeColors } from "./types/theme.types";
