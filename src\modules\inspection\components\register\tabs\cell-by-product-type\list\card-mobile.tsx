import { Badge } from "@/shared/components/shadcn/badge";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { Package } from "lucide-react";
import { ICellByProductTypeDto } from "../../../../../types/cell-by-product-type/dtos/find-all.dto";
import { CellByProductTypeActions } from "./actions";

interface ICellByProductTypeCardMobileProps {
	cellByProductType: ICellByProductTypeDto;
}

export const CellByProductTypeCardMobile = ({ cellByProductType }: ICellByProductTypeCardMobileProps) => {
	return (
		<Card className="relative border bg-white shadow-sm hover:shadow-md">
			<CardContent>
				<div className="mb-3 flex items-start justify-between">
					<h3 className="mr-2 line-clamp-2 flex-1 text-base leading-tight font-semibold">
						{cellByProductType.cellName}
						<Badge variant="secondary" className="ml-2 text-xs">
							<Package className="h-3 w-3" />
						</Badge>
					</h3>
					<div className="bg-primary h-1.5 w-1.5 rounded-full opacity-60" />
				</div>
				<div className="mb-4 space-y-2">
					<div className="flex items-center justify-between">
						<span className="text-xs text-gray-500">Célula</span>
						<Badge variant="outline" className="bg-gray-50 px-2 py-0.5 font-mono text-xs text-gray-700">
							{cellByProductType.cellName}
						</Badge>
					</div>
					<div className="flex items-center justify-between">
						<span className="text-xs text-gray-500">Tipo de Produto</span>
						<Badge variant="outline" className="bg-gray-50 px-2 py-0.5 font-mono text-xs text-gray-700">
							{cellByProductType.productTypeName}
						</Badge>
					</div>
				</div>
				<Separator className="my-4" />
				<div className="flex gap-1.5">
					<div className="flex-1">
						<CellByProductTypeActions id={String(cellByProductType.id)} name={cellByProductType.cellName} />
					</div>
				</div>
			</CardContent>
		</Card>
	);
};
