import { NextRequest, NextResponse } from "next/server";
import { UnifiedAuthMiddleware } from "./config/middleware/auth/unified-auth.middleware";
import { SecurityHeadersMiddleware } from "./config/middleware/security/security-headers.middleware";

export async function middleware(request: NextRequest): Promise<Response> {
	try {
		const authResponse = await UnifiedAuthMiddleware.process(request);
		if (authResponse.status >= 300 && authResponse.status < 400) return SecurityHeadersMiddleware.addToResponse(authResponse);
		return SecurityHeadersMiddleware.addToResponse(NextResponse.next());
	} catch (error) {
		console.error("Erro no middleware principal:", error);
		const errorResponse = NextResponse.redirect(new URL("/auth/login", request.url), { status: 302 });
		return SecurityHeadersMiddleware.addToResponse(errorResponse);
	}
}

export const config = {
	matcher: ["/((?!api|_next/static|_next/image|_next/font|favicon.ico|events/devtools/events|events$|\\.well-known).*)"],
};
