/**
 * Tipos base para o sistema de temas
 * Seguindo princípios SOLID e tipagem 100% TypeScript
 */

// Temas disponíveis no sistema
export type ThemeMode = "light" | "dark" | "system";
export type CustomThemeId = string;
export type ThemeId = ThemeMode | CustomThemeId;

// Interface para definição de cores de um tema
export interface IThemeColors {
	// Cores principais
	primary: string;
	primaryForeground: string;
	secondary: string;
	secondaryForeground: string;

	// Cores de fundo
	background: string;
	backgroundSecondary: string;
	foreground: string;

	// Cores de texto
	textPrimary: string;
	textSecondary: string;

	// Cores de borda e input
	border: string;
	input: string;
	ring: string;

	// Cores de acento
	accent: string;
	accentForeground: string;
	muted: string;
	mutedForeground: string;

	// Cores de destruição
	destructive: string;
	destructiveForeground: string;

	// Cores específicas do projeto
	leafGreenColor: string;

	// Cores de card
	card: string;
	cardForeground: string;

	// Cores de popover
	popover: string;
	popoverForeground: string;

	// Cores de chart
	chart1: string;
	chart2: string;
	chart3: string;
	chart4: string;
	chart5: string;

	// Cores de sidebar
	sidebar: string;
	sidebarForeground: string;
	sidebarPrimary: string;
	sidebarPrimaryForeground: string;
	sidebarAccent: string;
	sidebarAccentForeground: string;
	sidebarBorder: string;
	sidebarRing: string;
}

// Interface para configuração de um tema
export interface IThemeConfig {
	id: ThemeId;
	name: string;
	description?: string;
	colors: IThemeColors;
	isCustom: boolean;
	isSystemDefault?: boolean;
}

// Interface para o estado do tema
export interface IThemeState {
	currentTheme: ThemeId;
	availableThemes: IThemeConfig[];
	systemPreference: "light" | "dark";
	isLoading: boolean;
	error: string | null;
}

// Interface para persistência do tema
export interface IThemePersistence {
	theme: ThemeId;
	timestamp: number;
	version: string;
}

// Interface para configuração de persistência
export interface IThemePersistenceConfig {
	cookieName: string;
	localStorageKey: string;
	version: string;
	maxAge: number; // em segundos
}

// Interface para o serviço de temas
export interface IThemeService {
	getCurrentTheme(): ThemeId;
	setTheme(themeId: ThemeId): Promise<void>;
	getThemeConfig(themeId: ThemeId): IThemeConfig | null;
	getAllThemes(): IThemeConfig[];
	createCustomTheme(config: Omit<IThemeConfig, "id" | "isCustom">): Promise<IThemeConfig>;
	updateCustomTheme(id: CustomThemeId, config: Partial<IThemeConfig>): Promise<IThemeConfig>;
	deleteCustomTheme(id: CustomThemeId): Promise<void>;
	getSystemPreference(): "light" | "dark";
	applyThemeToDOM(themeId: ThemeId): void;
}

// Interface para o hook de tema
export interface IUseThemeHook {
	currentTheme: ThemeId;
	themeConfig: IThemeConfig | null;
	availableThemes: IThemeConfig[];
	systemPreference: "light" | "dark";
	isLoading: boolean;
	error: string | null;
	setTheme: (themeId: ThemeId) => Promise<void>;
	toggleTheme: () => Promise<void>;
	createCustomTheme: (config: Omit<IThemeConfig, "id" | "isCustom">) => Promise<IThemeConfig>;
	updateCustomTheme: (id: CustomThemeId, config: Partial<IThemeConfig>) => Promise<IThemeConfig>;
	deleteCustomTheme: (id: CustomThemeId) => Promise<void>;
}

// Interface para eventos de tema
export interface IThemeChangeEvent {
	previousTheme: ThemeId;
	currentTheme: ThemeId;
	timestamp: number;
}

// Interface para validação de tema
export interface IThemeValidation {
	isValid: boolean;
	errors: string[];
}

// Tipos para componentes de seleção de tema
export interface IThemeSelectorProps {
	variant?: "dropdown" | "toggle" | "grid";
	size?: "sm" | "md" | "lg";
	showCustomThemes?: boolean;
	showSystemOption?: boolean;
	className?: string;
	onThemeChange?: (themeId: ThemeId) => void;
}

export interface IThemeToggleProps {
	size?: "sm" | "md" | "lg";
	showLabel?: boolean;
	className?: string;
	onToggle?: (themeId: ThemeId) => void;
}

// Constantes de tema
export const THEME_CONSTANTS = {
	DEFAULT_THEME: "light" as const,
	SYSTEM_THEME: "system" as const,
	STORAGE_VERSION: "1.0.0",
	COOKIE_MAX_AGE: 365 * 24 * 60 * 60, // 1 ano em segundos
	TRANSITION_DURATION: 200, // ms
} as const;

// Tipos utilitários
export type ThemeColorKey = keyof IThemeColors;
export type ThemeConfigKey = keyof IThemeConfig;
export type ThemeStateKey = keyof IThemeState;
