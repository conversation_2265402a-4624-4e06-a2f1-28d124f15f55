import { useFindAllCollaborator } from "@/modules/collaborator/hooks/list/find-all.hook";
import { ICollaboratorDto } from "@/modules/collaborator/types/find-all.dto";
import { IPaginationParameters } from "@/shared/types/pagination/types";

export function useFindAllCollaboratorSelect(params: IPaginationParameters) {
	const { data, ...rest } = useFindAllCollaborator(params);

	const mappedData =
		data?.map((item: ICollaboratorDto) => ({
			id: item.document,
			name: item.name,
		})) ?? [];

	return { data: mappedData, ...rest };
}
