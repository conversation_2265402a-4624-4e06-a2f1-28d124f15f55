"use client";
import { INSPECTION_FORMS_LINKS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createGetRequest } from "@/shared/lib/requests";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";
import { IFormLinkDto } from "../../../types/form-link/dtos/find-all.dto";

export const useFindAllFormLinks = ({ page = 1, limit = 10, search = "" }: { page: number; limit: number; search: string }) => {
	const { canRead } = usePermissions();

	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.formsLink.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IFormLinkDto>>(INSPECTION_FORMS_LINKS_ENDPOINTS.FIND_ALL({ page, limit, search })),
		enabled: canRead(INSPECTION_SUBJECTS.INSPECTION_FORM),
	});

	const isNoDataFound = !data?.success && data?.status === 404;
	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
