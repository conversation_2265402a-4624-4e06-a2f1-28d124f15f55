import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, MoreHorizontal } from "lucide-react";
import React from "react";

import { cn } from "@/shared/lib/shadcn/utils";
import { Button } from "../../shadcn/button";
import { Label } from "../../shadcn/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../shadcn/select";

interface PaginationProps {
	currentPage: number;
	totalPages: number;
	pageSize: number;
	totalItems: number;
	selectedCount?: number;
	pageSizeOptions?: number[];
	onPageChange: (page: number) => void;
	onPageSizeChange: (pageSize: number) => void;
	showPageSizeSelector?: boolean;
	showSelectedInfo?: boolean;
	className?: string;
}

export type { PaginationProps };

export const Pagination: React.FC<PaginationProps> = ({
	currentPage,
	totalPages,
	pageSize,
	totalItems,
	selectedCount = 0,
	pageSizeOptions = [10, 20, 30, 40, 50],
	onPageChange,
	onPageSizeChange,
	showPageSizeSelector = true,
	showSelectedInfo = false,
	className,
}) => {
	const canPreviousPage = currentPage > 1;
	const canNextPage = currentPage < totalPages;
	const hasItems = totalItems > 0;

	const handlePageChange = (page: number) => {
		if (page >= 1 && page <= totalPages) {
			onPageChange(page);
		}
	};

	const handlePageSizeChange = (value: string) => {
		onPageSizeChange(Number(value));
	};

	const getItemRange = () => {
		if (!hasItems) return { start: 0, end: 0 };
		const start = (currentPage - 1) * pageSize + 1;
		const end = Math.min(currentPage * pageSize, totalItems);
		return { start, end };
	};

	const { start, end } = getItemRange();

	const getPageNumbers = () => {
		const pages: (number | "ellipsis")[] = [];
		const maxVisiblePages = 7;

		if (totalPages <= maxVisiblePages) {
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		} else {
			if (currentPage <= 4) {
				for (let i = 1; i <= 5; i++) {
					pages.push(i);
				}
				if (totalPages > 6) pages.push("ellipsis");
				pages.push(totalPages);
			} else if (currentPage >= totalPages - 3) {
				pages.push(1);
				if (totalPages > 6) pages.push("ellipsis");
				for (let i = totalPages - 4; i <= totalPages; i++) {
					pages.push(i);
				}
			} else {
				pages.push(1);
				pages.push("ellipsis");
				for (let i = currentPage - 1; i <= currentPage + 1; i++) {
					pages.push(i);
				}
				pages.push("ellipsis");
				pages.push(totalPages);
			}
		}

		return pages;
	};

	if (!hasItems) return null;

	return (
		<div className={cn("flex flex-col items-center justify-between gap-4 px-4 py-3 lg:flex-row", className)}>
			<div className="flex flex-col gap-2 text-sm lg:flex-row lg:items-center lg:gap-6">
				<div className="text-muted-foreground flex items-center gap-1">
					<span className="text-foreground font-medium">
						{start}-{end}
					</span>
					<span>de</span>
					<span className="text-foreground font-medium">{totalItems}</span>
					<span>itens</span>
				</div>

				{showSelectedInfo && selectedCount > 0 && (
					<div className="text-primary flex items-center gap-1">
						<span className="font-medium">{selectedCount}</span>
						<span className="text-muted-foreground">{selectedCount === 1 ? "item selecionado" : "itens selecionados"}</span>
					</div>
				)}
			</div>
			<div className="flex flex-col items-center gap-4 lg:flex-row lg:gap-6">
				{showPageSizeSelector && (
					<div className="flex items-center gap-2">
						<Label htmlFor="rows-per-page" className="text-muted-foreground text-sm font-medium">
							Linhas por página
						</Label>
						<Select value={`${pageSize}`} onValueChange={handlePageSizeChange}>
							<SelectTrigger size="sm" className="w-20" id="rows-per-page">
								<SelectValue placeholder={pageSize} />
							</SelectTrigger>
							<SelectContent side="top">
								{pageSizeOptions.map(size => (
									<SelectItem key={size} value={`${size}`}>
										{size}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				)}

				<div className="flex items-center gap-1">
					<Button
						variant="outline"
						size="sm"
						className="h-8 w-8 p-0 transition-all hover:scale-105"
						onClick={() => handlePageChange(1)}
						disabled={!canPreviousPage}
						title="Primeira página"
					>
						<ChevronsLeft className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="sm"
						className="h-8 w-8 p-0 transition-all hover:scale-105"
						onClick={() => handlePageChange(currentPage - 1)}
						disabled={!canPreviousPage}
						title="Página anterior"
					>
						<ChevronLeft className="h-4 w-4" />
					</Button>
					<div className="hidden items-center gap-1 md:flex">
						{getPageNumbers().map((page, index) =>
							page === "ellipsis" ? (
								<div key={`ellipsis-${index}`} className="flex h-8 w-8 items-center justify-center">
									<MoreHorizontal className="text-muted-foreground h-4 w-4" />
								</div>
							) : (
								<Button
									key={page}
									variant={page === currentPage ? "default" : "outline"}
									size="sm"
									className={cn(
										"h-8 w-8 p-0 transition-all hover:scale-105",
										page === currentPage && "bg-primary text-primary-foreground shadow-sm",
									)}
									onClick={() => handlePageChange(page)}
									title={`Página ${page}`}
								>
									{page}
								</Button>
							),
						)}
					</div>
					<div className="flex h-8 items-center px-3 text-sm font-medium md:hidden">
						{currentPage} / {totalPages}
					</div>
					<Button
						variant="outline"
						size="sm"
						className="h-8 w-8 p-0 transition-all hover:scale-105"
						onClick={() => handlePageChange(currentPage + 1)}
						disabled={!canNextPage}
						title="Próxima página"
					>
						<ChevronRight className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="sm"
						className="h-8 w-8 p-0 transition-all hover:scale-105"
						onClick={() => handlePageChange(totalPages)}
						disabled={!canNextPage}
						title="Última página"
					>
						<ChevronsRight className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
};
