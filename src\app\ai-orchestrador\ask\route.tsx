import { refreshRequest } from "@/core/auth/api/requests/refresh";
import { getAllCookies } from "@/shared/lib/cookies/crud/get";
import { CookieHeaderService } from "@/shared/lib/cookies/services/cookie-header.service";
import { NextRequest } from "next/server";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
	try {
		const body = await request.text();
		const buildCookieHeader = async (): Promise<string> => {
			const cookieService = new CookieHeaderService();
			const cookiesData = await getAllCookies();
			return cookieService.processCookieData(cookiesData) || "";
		};

		const performAskRequest = async (cookieHeader: string) => {
			return fetch(`${process.env.BACKEND_URL}/ai/ask`, {
				method: "POST",
				headers: {
					Accept: "*/*",
					Cookie: cookieHeader,
					"Content-Type": "application/json",
				},
				body,
			});
		};

		const cookieHeader = await buildCookieHeader();
		let response = await performAskRequest(cookieHeader);
		if (response.status === 401) {
			const refreshResult = await refreshRequest();
			if (!refreshResult.success) {
				return new Response(JSON.stringify({ error: refreshResult.data?.message || "Falha ao renovar a sessão" }), {
					status: refreshResult.status || 401,
				});
			}
			const newCookieHeader = await buildCookieHeader();
			response = await performAskRequest(newCookieHeader);
		}

		if (!response.ok)
			return new Response(JSON.stringify({ error: `Erro ao conectar com a API de IA ${response.statusText}` }), { status: response.status });
		return new Response(response.body, {
			headers: {
				"Content-Type": response.headers.get("content-type") || "text/event-stream",
				"Cache-Control": "no-cache, no-transform",
				Connection: "keep-alive",
				"X-Accel-Buffering": "no",
			},
		});
	} catch (error) {
		console.error("Erro na API route:", error);
		return new Response(JSON.stringify({ error: "Erro interno do servidor" }), { status: 500 });
	}
}
