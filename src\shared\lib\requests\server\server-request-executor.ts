"use server";

import { ApiResponse } from "@/shared/types/requests/request.type";

type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

export async function executeServerRequest<TSuccess>(requestParams: {
	method: string;
	path: string;
	body?: unknown;
	options?: Record<string, unknown>;
}): Promise<ApiResponse<TSuccess>> {
	try {
		// Importa dinamicamente o createRequest para evitar problemas de bundling
		const { createRequest } = await import("../index");

		const response = await createRequest<TSuccess>({
			...requestParams.options,
			path: requestParams.path,
			method: requestParams.method as HttpMethod,
			body: requestParams.body,
		});

		// Garantir que apenas dados serializáveis sejam retornados
		return {
			success: response.success,
			data: JSON.parse(JSON.stringify(response.data)),
			status: response.status,
		} as ApiResponse<TSuccess>;
	} catch (error) {
		return {
			success: false,
			data: {
				message: "Erro ao executar requisição no servidor",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			status: 500,
		} as ApiResponse<TSuccess>;
	}
}

export async function executeServerGetRequest<TSuccess>(path: string, options?: Record<string, unknown>): Promise<ApiResponse<TSuccess>> {
	return executeServerRequest<TSuccess>({
		method: "GET",
		path,
		options,
	});
}

export async function executeServerPostRequest<TSuccess>(path: string, body?: unknown, options?: Record<string, unknown>): Promise<ApiResponse<TSuccess>> {
	return executeServerRequest<TSuccess>({
		method: "POST",
		path,
		body,
		options,
	});
}

export async function executeServerPutRequest<TSuccess>(path: string, body?: unknown, options?: Record<string, unknown>): Promise<ApiResponse<TSuccess>> {
	return executeServerRequest<TSuccess>({
		method: "PUT",
		path,
		body,
		options,
	});
}

export async function executeServerPatchRequest<TSuccess>(path: string, body?: unknown, options?: Record<string, unknown>): Promise<ApiResponse<TSuccess>> {
	return executeServerRequest<TSuccess>({
		method: "PATCH",
		path,
		body,
		options,
	});
}

export async function executeServerDeleteRequest<TSuccess>(path: string, options?: Record<string, unknown>): Promise<ApiResponse<TSuccess>> {
	return executeServerRequest<TSuccess>({
		method: "DELETE",
		path,
		options,
	});
}
