import { createCookie } from "@/shared/lib/cookies/crud/create";
import { ICreateCookieProps } from "@/shared/lib/cookies/types";
import { cookies as cookiesModule } from "next/headers";

jest.mock("next/headers", () => ({
	cookies: jest.fn(),
}));

const mockSet = jest.fn();

beforeEach(() => {
	jest.clearAllMocks();
	(cookiesModule as jest.Mock).mockResolvedValue({
		set: mockSet,
	});
});

describe("createCookie", () => {
	const defaultData: ICreateCookieProps = {
		name: "testCookie",
		value: "testValue",
		options: {
			maxAge: 3600,
		},
	};

	it("deve criar um cookie com sucesso", async () => {
		const { status, success } = await createCookie(defaultData);
		expect(cookiesModule).toHaveBeenCalled();
		expect(mockSet).toHaveBeenCalledWith(defaultData.name, defaultData.value, {
			...defaultData.options,
			httpOnly: true,
			path: "/",
			sameSite: "lax",
			secure: false,
		});
		expect(success).toBe(true);
		expect(status).toBe(201);
	});

	it("deve falhar ao criar um cookie", async () => {
		(cookiesModule as jest.Mock).mockRejectedValueOnce(new Error("Ocorreu um erro ao criar o cookie"));
		const { status, success } = await createCookie(defaultData);
		expect(cookiesModule).toHaveBeenCalled();
		expect(mockSet).not.toHaveBeenCalled();
		expect(success).toBe(false);
		expect(status).toBe(500);
	});
});
