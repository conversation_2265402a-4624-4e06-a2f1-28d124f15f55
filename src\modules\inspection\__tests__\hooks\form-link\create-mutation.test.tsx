import { useCreateFormLinkMutation } from "@/modules/inspection/hooks/form-links/create/mutation.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";

jest.mock("@/shared/lib/requests", () => ({
	createPostRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canCreate: () => true })),
}));

const mockedCreatePostRequest = createPostRequest as jest.MockedFunction<typeof createPostRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Vínculo de formulário criado com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Error ao vincular o formulário" };

describe("useCreateFormLink", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve criar o vínculo de formulário com sucesso", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useCreateFormLinkMutation(() => {}), { wrapper });
		await expect(
			result.current.createFormLink({
				formId: 1,
				cellId: 2,
				activityId: 123,
				cellByComponentId: 3,
				cellByProductTypeId: 4,
			}),
		).resolves.toEqual(mockSuccess);
	});

	it("deve lançar erro ao falhar", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useCreateFormLinkMutation(() => {}), { wrapper });
		await expect(result.current.createFormLink({ formId: 1, cellId: 2, activityId: 123, cellByComponentId: 3, cellByProductTypeId: 4 })).rejects.toThrow(
			"Error ao vincular o formulário",
		);
	});
});
