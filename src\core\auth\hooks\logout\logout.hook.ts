import { useNavigatePaths } from "@/shared/hooks/utils";
import { createDeleteRequest } from "@/shared/lib/requests";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAtomValue, useSetAtom } from "jotai";
import { AUTH_ENDPOINTS } from "../../api/endpoints";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { isLogoutLoadingAtom, logoutLoadingMessageAtom } from "../../atoms/logout-loading.atom";
import { userAtom } from "../../atoms/user.atom";

interface ILogoutResponse {
	success: boolean;
}

interface IUseLogoutHook {
	logout: () => Promise<void>;
	isLoggingOut: boolean;
}

export const useLogout = (): IUseLogoutHook => {
	const { replaceToCurrent } = useNavigatePaths();
	const queryClient = useQueryClient();
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const setIsLogoutLoading = useSetAtom(isLogoutLoadingAtom);
	const setLogoutLoadingMessage = useSetAtom(logoutLoadingMessageAtom);
	const isLogoutLoading = useAtomValue(isLogoutLoadingAtom);

	const handleLogoutSuccess = async (): Promise<void> => {
		setIsAuthenticated(false);
		setUser(null);
		queryClient.clear();
		replaceToCurrent();
		setIsLogoutLoading(false);
	};

	const logoutMutation = useMutation<ILogoutResponse, Error>({
		mutationFn: async (): Promise<ILogoutResponse> => {
			const response = await createDeleteRequest(AUTH_ENDPOINTS.LOGOUT);
			await handleLogoutSuccess();
			return response;
		},
		onError: () => {
			setIsLogoutLoading(false);
		},
	});

	return {
		logout: async (): Promise<void> => {
			try {
				setLogoutLoadingMessage("Saindo...");
				setIsLogoutLoading(true);
				await logoutMutation.mutateAsync();
			} catch (error) {
				throw error;
			}
		},
		isLoggingOut: isLogoutLoading,
	};
};
