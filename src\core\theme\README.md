# Sistema de Múltiplos Temas - S.I.M³P

Sistema robusto e escalável para gerenciamento de múltiplos temas em Next.js 15 com Tailwind CSS v4 e Jotai.

## 🎯 Características

- ✅ **Te<PERSON>**: Light, Dark e System
- ✅ **Temas Customizados**: Criação e gerenciamento dinâmico
- ✅ **SSR Compatível**: Sem flash de tema incorreto
- ✅ **Persistência Híbrida**: Cookies + localStorage
- ✅ **Performance Otimizada**: Transições suaves e carregamento eficiente
- ✅ **TypeScript 100%**: Tipagem completa sem `any`
- ✅ **Princípios SOLID**: Arquitetura limpa e extensível

## 🏗️ Arquitetura

```
src/core/theme/
├── atoms/                 # Estado global com Jotai
│   ├── theme.atom.ts
│   └── theme-persistence.atom.ts
├── services/              # Lógica de negócio
│   ├── theme.service.ts
│   └── theme-persistence.service.ts
├── providers/             # Integração React
│   └── theme.provider.tsx
├── hooks/                 # Interface para componentes
│   └── use-theme.hook.ts
├── components/            # UI Components
│   ├── theme-toggle.tsx
│   └── theme-selector.tsx
├── types/                 # Tipagem TypeScript
│   └── theme.types.ts
└── constants/             # Configurações
    └── themes.constants.ts
```

## 🚀 Uso Básico

### 1. Hook Principal

```tsx
import { useTheme } from "@/core/theme/hooks/use-theme.hook";

function MyComponent() {
  const { 
    currentTheme, 
    setTheme, 
    availableThemes, 
    isLoading 
  } = useTheme();

  return (
    <div>
      <p>Tema atual: {currentTheme}</p>
      <button onClick={() => setTheme("dark")}>
        Tema Escuro
      </button>
    </div>
  );
}
```

### 2. Componentes Prontos

```tsx
import { ThemeToggle, ThemeSelector } from "@/core/theme/components";

function Header() {
  return (
    <div className="flex items-center gap-4">
      {/* Toggle simples */}
      <ThemeToggle size="md" showLabel={true} />
      
      {/* Seletor completo */}
      <ThemeSelector 
        variant="dropdown" 
        showCustomThemes={true}
        onThemeChange={(theme) => console.log("Tema alterado:", theme)}
      />
    </div>
  );
}
```

### 3. Hooks Especializados

```tsx
// Toggle simples entre light/dark
import { useThemeToggle } from "@/core/theme/hooks/use-theme.hook";

function SimpleToggle() {
  const { toggle, isDark, isLoading } = useThemeToggle();
  
  return (
    <button onClick={toggle} disabled={isLoading}>
      {isDark ? "🌙" : "☀️"}
    </button>
  );
}

// Gerenciamento de temas customizados
import { useCustomThemes } from "@/core/theme/hooks/use-theme.hook";

function CustomThemeManager() {
  const { customThemes, createCustomTheme } = useCustomThemes();
  
  const handleCreate = async () => {
    await createCustomTheme({
      name: "Meu Tema",
      description: "Tema personalizado",
      colors: {
        primary: "#ff6b6b",
        background: "#f8f9fa",
        // ... outras cores
      }
    });
  };
  
  return (
    <div>
      <button onClick={handleCreate}>Criar Tema</button>
      {customThemes.map(theme => (
        <div key={theme.id}>{theme.name}</div>
      ))}
    </div>
  );
}
```

## 🎨 Integração com Tailwind CSS v4

### CSS Variables Dinâmicas

O sistema utiliza CSS variables que são atualizadas dinamicamente:

```css
/* Tema Light */
.theme-light {
  --primary: #004475;
  --background: #f9f9fa;
  --foreground: #000000;
}

/* Tema Dark */
.theme-dark {
  --primary: #004475;
  --background: #000000;
  --foreground: #ffffff;
}

/* Integração com Tailwind v4 */
@theme inline {
  --color-primary: var(--primary);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}
```

### Classes Tailwind

Use as classes normalmente - elas se adaptam automaticamente:

```tsx
<div className="bg-background text-foreground border-border">
  <h1 className="text-primary">Título</h1>
  <p className="text-secondary">Texto secundário</p>
</div>
```

## 🔧 Configuração Avançada

### Temas Customizados

```tsx
import { themeService } from "@/core/theme/services/theme.service";

// Criar tema customizado
const customTheme = await themeService.createCustomTheme({
  name: "Tema Corporativo",
  description: "Cores da empresa",
  colors: {
    primary: "#1e40af",
    primaryForeground: "#ffffff",
    background: "#f8fafc",
    backgroundSecondary: "#e2e8f0",
    // ... todas as outras cores obrigatórias
  }
});

// Aplicar tema
await themeService.setTheme(customTheme.id);
```

### Persistência Customizada

```tsx
import { themePersistenceService } from "@/core/theme/services/theme-persistence.service";

// Configuração personalizada
const customPersistence = new ThemePersistenceService({
  cookieName: "my-app-theme",
  localStorageKey: "my-app-theme-preference",
  version: "2.0.0",
  maxAge: 30 * 24 * 60 * 60, // 30 dias
});

// Uso manual
await customPersistence.syncTheme("dark");
const savedTheme = await customPersistence.getThemeWithFallback();
```

## 🌐 SSR e Server Components

### Layout com SSR

```tsx
// app/layout.tsx
import { cookies } from "next/headers";
import { themePersistenceService } from "@/core/theme/services/theme-persistence.service";

export default async function RootLayout({ children }) {
  // Carrega tema do servidor
  const serverTheme = await themePersistenceService.loadThemeFromServer();
  
  return (
    <html lang="pt-BR" data-theme={serverTheme || "light"}>
      <body className="bg-background text-foreground">
        <ProviderGlobal>
          {children}
        </ProviderGlobal>
      </body>
    </html>
  );
}
```

### Middleware para Detecção

```tsx
// middleware.ts
import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
  const theme = request.cookies.get("simp-theme")?.value;
  
  if (!theme) {
    // Detecta preferência do sistema via headers
    const acceptHeader = request.headers.get("accept") || "";
    const prefersDark = acceptHeader.includes("dark");
    
    const response = NextResponse.next();
    response.cookies.set("simp-theme", prefersDark ? "dark" : "light");
    return response;
  }
  
  return NextResponse.next();
}
```

## 📊 Performance e Otimizações

### Lazy Loading

```tsx
import { lazy, Suspense } from "react";

const ThemeSelector = lazy(() => 
  import("@/core/theme/components/theme-selector").then(m => ({
    default: m.ThemeSelector
  }))
);

function MyComponent() {
  return (
    <Suspense fallback={<div>Carregando...</div>}>
      <ThemeSelector />
    </Suspense>
  );
}
```

### Memoização

```tsx
import { memo } from "react";
import { useTheme } from "@/core/theme/hooks/use-theme.hook";

const ThemeAwareComponent = memo(function ThemeAwareComponent() {
  const { currentTheme } = useTheme();
  
  return <div data-theme={currentTheme}>Conteúdo</div>;
});
```

## 🔍 Debug e Desenvolvimento

### Hook de Debug

```tsx
import { useThemeDebug } from "@/core/theme/hooks/use-theme.hook";

function DebugPanel() {
  const debug = useThemeDebug();
  
  if (!debug) return null; // Só em desenvolvimento
  
  return (
    <div className="fixed bottom-4 right-4 p-4 bg-card border rounded">
      <button onClick={debug.logState}>
        Log Theme State
      </button>
      <pre>{JSON.stringify(debug.themeState, null, 2)}</pre>
    </div>
  );
}
```

### Validação de Temas

```tsx
import { useThemeValidation } from "@/core/theme/hooks/use-theme.hook";

function ThemeValidator() {
  const { validateThemeConfig } = useThemeValidation();
  
  const handleValidate = (config) => {
    const result = validateThemeConfig(config);
    if (!result.isValid) {
      console.error("Tema inválido:", result.errors);
    }
  };
  
  return <div>Validador de temas</div>;
}
```

## 🚨 Tratamento de Erros

```tsx
import { useTheme } from "@/core/theme/hooks/use-theme.hook";

function ErrorHandling() {
  const { error, setTheme } = useTheme();
  
  const handleThemeChange = async (themeId) => {
    try {
      await setTheme(themeId);
    } catch (error) {
      console.error("Erro ao alterar tema:", error);
      // Mostrar toast de erro
    }
  };
  
  if (error) {
    return <div className="text-destructive">Erro: {error}</div>;
  }
  
  return <div>Interface normal</div>;
}
```

## 📱 Responsividade

```tsx
// Temas diferentes por dispositivo
import { useMediaQuery } from "@/shared/hooks/use-media-query";

function ResponsiveTheme() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const { setTheme } = useTheme();
  
  useEffect(() => {
    if (isMobile) {
      setTheme("mobile-optimized");
    }
  }, [isMobile, setTheme]);
  
  return <div>Conteúdo responsivo</div>;
}
```
