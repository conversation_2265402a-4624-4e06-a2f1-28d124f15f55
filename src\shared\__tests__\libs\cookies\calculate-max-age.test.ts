import { calculateMaxAge } from "../../../lib/cookies/utils/calculate-max-age";
import { getJWTExpiration } from "../../../lib/jwt/expiration-time";

jest.mock("../../../lib/jwt/expiration-time", () => ({
	getJWTExpiration: jest.fn(),
}));

const mockedGetJWTExpiration = getJWTExpiration as jest.Mock;

describe("calculateMaxAge", () => {
	afterEach(() => {
		jest.resetAllMocks();
		jest.restoreAllMocks();
	});

	it("retorna nulo quando getJWTExpiration retorna nulo", () => {
		mockedGetJWTExpiration.mockReturnValue(null);
		const result = calculateMaxAge("dummy-token");
		expect(result).toBeNull();
		expect(mockedGetJWTExpiration).toHaveBeenCalledWith("dummy-token");
	});

	it("retorna nulo quando o token já expirou", () => {
		const now = 1_600_000_000_000;
		jest.spyOn(Date, "now").mockReturnValue(now);
		const expiredDate = new Date(now - 1000);
		mockedGetJWTExpiration.mockReturnValue(expiredDate);
		const result = calculateMaxAge("expired-token");
		expect(result).toBeNull();
		expect(mockedGetJWTExpiration).toHaveBeenCalledWith("expired-token");
	});

	it("retorna o piso da diferença em segundos para uma expiração futura", () => {
		const now = 1_600_000_000_000;
		jest.spyOn(Date, "now").mockReturnValue(now);
		const futureDate = new Date(now + 5_500);
		mockedGetJWTExpiration.mockReturnValue(futureDate);
		const result = calculateMaxAge("future-token");
		expect(result).toBe(5);
		expect(mockedGetJWTExpiration).toHaveBeenCalledWith("future-token");
	});

	it("retorna 0 quando a expiração está no futuro mas falta menos de um segundo", () => {
		const now = 1_600_000_000_000;
		jest.spyOn(Date, "now").mockReturnValue(now);
		const nearFuture = new Date(now + 500);
		mockedGetJWTExpiration.mockReturnValue(nearFuture);
		const result = calculateMaxAge("near-future-token");
		expect(result).toBe(0);
		expect(mockedGetJWTExpiration).toHaveBeenCalledWith("near-future-token");
	});
});
