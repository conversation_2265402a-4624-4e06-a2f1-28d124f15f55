import { IChatMessage } from "../types/messages.type";
import { IChatStreamResponse } from "../types/streaming.type";

interface IStreamingChunkHandler {
	chunkData: IChatStreamResponse;
	updateMessage: (id: string, updates: Partial<IChatMessage>) => void;
	assistantMessageId: string;
	currentValue: string;
}

export const handleStreamingChunk = ({ chunkData, updateMessage, assistantMessageId, currentValue }: IStreamingChunkHandler) => {
	updateMessage(assistantMessageId, {
		content: currentValue,
		isStreaming: chunkData.type !== "complete",
	});
};
