import z from "zod";

export const createCollabSectorSchema = z.object({
	collaborator: z.object({
		id: z.string(),
		name: z.string(),
	}),
	sector: z.object({ id: z.number(), name: z.string() }),
	pin: z.string().min(4, "O PIN (os 4 primeiros dígitos do CPF) do colaborador é obrigatório.").max(4, "O PIN deve ter exatamente 4 dígitos."),
});

export type TCreateCollabSectorSchema = z.infer<typeof createCollabSectorSchema>;
