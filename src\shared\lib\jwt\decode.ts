export const decodeJWT = <T = Record<string, unknown>>(token: string | null): T | null => {
	try {
		if (!token) return null;
		const base64Url = token.split(".")[1];
		if (!base64Url) return null;
		const base64 = base64Url?.replace(/-/g, "+").replace(/_/g, "/");
		const jsonPayload = Buffer.from(base64, "base64").toString("utf8");
		return JSON.parse(jsonPayload) as T;
	} catch (error) {
		console.error("O JWT fornecido é inválido", error);
		return null;
	}
};
