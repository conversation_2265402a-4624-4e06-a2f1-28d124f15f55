import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IPaginationParameters } from "@/shared/types/pagination/types";

const BASE = "/product-types";

export const PRODUCT_TYPE_ENDPOINTS = Object.freeze({
	CREATE: BASE,
	FIND_ALL: (params?: IPaginationParameters) => buildQueryParams(BASE, { ...params }),
	DELETE: (id: string) => `${BASE}/${encodeURIComponent(id)}`,
} as const);
