import { IChatKnowledgeDto } from "@/modules/chat/types/dtos/find-all-knowledge.dto";
import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";
import { TableLoading } from "@/shared/components/custom/loading";
import { Pagination } from "@/shared/components/custom/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ComponentIcon } from "lucide-react";
import { chatAdminColumns } from "./columns";

interface DesktopTableViewProps {
	data: IChatKnowledgeDto[] | undefined;
	isLoading: boolean;
	hasError: boolean;
	error: string | false | undefined;
	searchTerm: string;
	pageSize: number;
	pagination: {
		currentPage: number;
		totalPages: number;
		itemsPerPage: number;
		totalItems: number;
	} | null;
	onPageChange: (page: number) => void;
	onPageSizeChange: (size: number) => void;
	onRowClick: (id: string) => void;
	canUpdate: boolean;
}

export const DesktopTableView = ({
	data,
	isLoading,
	hasError,
	error,
	searchTerm,
	pageSize,
	pagination,
	onPageChange,
	onPageSizeChange,
	onRowClick,
	canUpdate,
}: DesktopTableViewProps) => {
	const table = useReactTable({
		data: data ?? [],
		columns: chatAdminColumns,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const renderTableBody = () => {
		if (hasError) {
			return (
				<TableRow>
					<TableCell colSpan={chatAdminColumns.length} className="h-24 text-center text-red-500">
						Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
					</TableCell>
				</TableRow>
			);
		}

		if (isLoading) {
			return <TableLoading columns={chatAdminColumns.length} rows={pageSize} />;
		}

		if (table.getRowModel().rows.length) {
			return table.getRowModel().rows.map(row => {
				const clickable = canUpdate;
				return (
					<TableRow
						key={row.id}
						data-state={row.getIsSelected() && "selected"}
						className={`transition-colors ${clickable ? "hover:bg-primary/5 cursor-pointer" : ""}`}
						onClick={() => {
							if (!clickable) return;
							onRowClick(String(row.original.id));
						}}
					>
						{row.getVisibleCells().map(cell => (
							<TableCell key={cell.id} className="px-4 text-center align-middle" title={String(cell.getValue() ?? "")}>
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</TableCell>
						))}
					</TableRow>
				);
			});
		}

		return (
			<TableRow>
				<TableCell colSpan={chatAdminColumns.length} className="h-24 text-center">
					<EmptyStateTable
						searchTerm={searchTerm}
						icon={<ComponentIcon />}
						title="Nenhum conhecimento encontrado"
						description={searchTerm ? "Nenhum conhecimento corresponde ao termo pesquisado." : "Ainda não há conhecimentos cadastrados."}
						tip="Você pode tentar pesquisar por outros termos ou adicionar um novo conhecimento."
					/>
				</TableCell>
			</TableRow>
		);
	};

	return (
		<div className="space-y-4">
			<div className="bg-background rounded-controls overflow-x-auto border">
				<Table className="table-fixed">
					<TableHeader className="bg-primary sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => {
									const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
									const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
									return (
										<TableHead
											key={header.id}
											colSpan={header.colSpan}
											style={style}
											className="font-semibold whitespace-nowrap text-white"
										>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									);
								})}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>{renderTableBody()}</TableBody>
				</Table>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					onPageChange={onPageChange}
					onPageSizeChange={size => {
						onPageSizeChange(size);
						onPageChange(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
