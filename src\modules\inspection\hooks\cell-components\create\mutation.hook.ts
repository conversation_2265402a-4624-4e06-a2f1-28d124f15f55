import { SUBJECTS } from "@/config/permissions";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../../core/toast";
import { createPostRequest } from "../../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { CELL_COMPONENTS_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";
import { TCreateCellComponentForm } from "../../../validators/cell-components/create";

export const useCreateCellByComponentMutation = () => {
	const queryClient = useQueryClient();
	const { canCreate } = usePermissions();

	const createFormMutation = useMutation({
		mutationKey: inspectionKeys.cellByComponents.custom("create"),
		mutationFn: async (formData: TCreateCellComponentForm) => {
			if (!canCreate(SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_COMPONENT)) throw new Error("Você não tem permissão para criar componentes");
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(CELL_COMPONENTS_ENDPOINTS.CREATE, formData);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.cellByComponents.invalidateAll(queryClient),
	});

	return {
		createCellComponent: (formData: TCreateCellComponentForm) =>
			toast.promise(createFormMutation.mutateAsync(formData), {
				loading: "Criando componente...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
