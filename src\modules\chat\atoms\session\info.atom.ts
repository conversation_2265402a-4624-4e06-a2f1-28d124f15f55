import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { IChatMessage } from "../../types/messages.type";
import { IChatSession } from "../../types/session.type";

export const chatStreamingSessionInfo = atomWithStorage<IChatSession | null>("chat-streaming-session", null);
export const chatStreamingMessagesAtom = atom<IChatMessage[]>(get => get(chatStreamingSessionInfo)?.messages || []);
export const isMessagesAvailableAtom = atom<boolean>(get => get(chatStreamingMessagesAtom).length > 0);
export const sessionIdAtom = atom<string | null>(get => get(chatStreamingSessionInfo)?.id || null);
