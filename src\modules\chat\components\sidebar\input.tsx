import { SendHorizonal, Square } from "lucide-react";
import React, { useState } from "react";
import { Button } from "../../../../shared/components/shadcn/button";
import { Textarea } from "../../../../shared/components/shadcn/textarea";
import { cn } from "../../../../shared/lib/shadcn/utils";
import { useChatInput } from "../../hooks/handlers/handle-input.hook";

interface IChatInputProps {
	onSend: (content: string) => Promise<void>;
	onStop?: () => void;
	disabled?: boolean;
	placeholder?: string;
	isStreaming?: boolean;
}

export const ChatInput = React.forwardRef<HTMLTextAreaElement, IChatInputProps>(({ onSend, onStop, disabled, placeholder, isStreaming }, ref) => {
	const [inputValue, setInputValue] = useState("");

	const { canSend, canStop, handleKeyDown, handleSend, handleStop } = useChatInput({
		value: inputValue,
		onChange: setInputValue,
		onSend,
		onStop,
		disabled,
		isStreaming,
	});

	return (
		<div
			className={cn(
				`relative flex items-end gap-3 overflow-hidden border-t border-slate-200 bg-gradient-to-r from-slate-50 via-white to-slate-50 px-6 py-4 shadow-sm backdrop-blur-md transition-all duration-300 ease-in-out dark:border-slate-700 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900`,
			)}
		>
			<div className="from-primary/5 to-primary/5 pointer-events-none absolute inset-0 bg-gradient-to-r via-transparent" />
			<div className="via-primary/30 absolute top-0 right-0 left-0 h-px bg-gradient-to-r from-transparent to-transparent" />
			<div className="relative z-10 flex-1">
				<Textarea
					ref={ref}
					value={inputValue}
					onChange={e => setInputValue(e.target.value)}
					onKeyDown={handleKeyDown}
					placeholder={placeholder}
					disabled={disabled}
					className="focus:border-primary dark:focus:border-primary focus:ring-primary/20 max-h-32 min-h-[48px] resize-none overflow-y-auto rounded-xl border-slate-200 bg-white px-4 py-3 text-slate-700 shadow-sm transition-all duration-200 placeholder:text-slate-400 hover:shadow-md focus:shadow-md focus:ring-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-200 dark:placeholder:text-slate-500"
					rows={1}
				/>
			</div>
			{canStop ? (
				<Button
					onClick={handleStop}
					size="sm"
					className="group relative z-10 h-12 w-12 shrink-0 self-end rounded-xl bg-red-500 p-0 shadow-sm transition-all duration-200 hover:bg-red-600 hover:shadow-md"
					aria-label="Parar streaming"
				>
					<Square className="h-6 w-6 transition-transform duration-200 group-hover:scale-110" />
				</Button>
			) : (
				<Button
					onClick={handleSend}
					disabled={!canSend}
					size="sm"
					className="bg-primary hover:bg-primary/90 group relative z-10 h-12 w-12 shrink-0 self-end rounded-xl p-0 shadow-sm transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed disabled:bg-slate-300 dark:disabled:bg-slate-600"
					aria-label="Enviar mensagem"
				>
					<SendHorizonal className="h-6 w-6 transition-transform duration-200 group-hover:translate-x-0.5 group-hover:scale-110" />
				</Button>
			)}
		</div>
	);
});

ChatInput.displayName = "ChatInput";
