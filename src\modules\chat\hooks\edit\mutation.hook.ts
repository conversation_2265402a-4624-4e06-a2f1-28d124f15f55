"use client";

import { toast } from "@/core/toast";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createPatchRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { chatKeys } from "../../constants/query";
import { TEditKnowledgeSchema } from "../../validators/edit-knowledge";

export const useEditKnowledgeMutation = (onClose: () => void) => {
	const queryClient = useQueryClient();
	const { canUpdate } = usePermissions();

	const mutation = useMutation<IMessageGlobalReturn, Error, { id: string; form: TEditKnowledgeSchema }>({
		mutationKey: chatKeys.custom("edit-knowledge"),
		mutationFn: async ({ id, form }) => {
			if (!canUpdate("all")) throw new Error("Você não tem permissão para editar conhecimento. Apenas administradores podem realizar esta ação.");
			const { data, success } = await createPatchRequest<IMessageGlobalReturn>(CHAT_ENDPOINTS.UPDATE_KNOWLEDGE(id), form);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => {
			chatKeys.invalidateAll(queryClient);
			onClose();
		},
	});

	return {
		updateKnowledge: (id: string, form: TEditKnowledgeSchema) =>
			toast.promise(mutation.mutateAsync({ id, form }), {
				loading: "Atualizando conhecimento...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
