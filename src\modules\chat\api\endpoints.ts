import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IPaginationParameters } from "@/shared/types/pagination/types";

const BASE = "/ai";

export const CHAT_ENDPOINTS = Object.freeze({
	ASK: `${BASE}-orchestrador/ask`,
	ADD_KNOWLEDGE: `${BASE}/knowledge`,
	FIND_ALL_KNOWLEDGE: (params?: IPaginationParameters) => buildQueryParams(`${BASE}/knowledge`, { ...params }),
	FIND_KNOWLEDGE_BY_ID: (id: string) => `${BASE}/knowledge/${encodeURIComponent(id)}`,
	REMOVE_KNOWLEDGE: (id: string) => `${BASE}/knowledge/${encodeURIComponent(id)}`,
	UPDATE_KNOWLEDGE: (id: string) => `${BASE}/knowledge/${encodeURIComponent(id)}`,
} as const);
