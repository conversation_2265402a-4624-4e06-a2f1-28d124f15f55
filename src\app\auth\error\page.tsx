import { Button } from "@/shared/components/shadcn/button";
import { <PERSON>ert<PERSON>riangle, Clock, Server, Shield, WifiOff } from "lucide-react";
import Link from "next/link";
import type { ReactElement } from "react";

interface IAuthErrorPageProps {
	searchParams: Promise<{
		error?: string;
		message?: string;
		status?: string;
		redirect?: string;
	}>;
}

const AUTH_ERROR_CONFIG = {
	backend_error: {
		icon: Server,
		title: "Problema no Servidor",
		defaultMessage: "Não foi possível conectar ao servidor de autenticação. Tente novamente em alguns instantes.",
	},
	keycloak_error: {
		icon: Shield,
		title: "Erro de Autenticação",
		defaultMessage: "Ocorreu um erro interno durante o processo de autenticação. Tente fazer login novamente.",
	},
	invalid_redirect: {
		icon: AlertTriangle,
		title: "Redirecionamento Inválido",
		defaultMessage: "O endereço de redirecionamento não é válido. Você será direcionado para a página inicial.",
	},
	network_error: {
		icon: WifiOff,
		title: "Sem Conexão",
		defaultMessage: "Não foi possível conectar ao servidor. Verifique sua conexão com a internet e tente novamente.",
	},
	timeout_error: {
		icon: Clock,
		title: "Tempo Esgotado",
		defaultMessage: "A conexão com o servidor demorou muito para responder. Tente novamente.",
	},
} as const;

const getErrorConfig = (error?: string, status?: string) => {
	if (error === "backend_error") {
		const statusCode = status ? parseInt(status) : 0;
		if (statusCode === 0 || statusCode === 502 || statusCode === 503 || statusCode === 504) return AUTH_ERROR_CONFIG.network_error;
		if (statusCode === 408 || statusCode === 524) return AUTH_ERROR_CONFIG.timeout_error;
		return AUTH_ERROR_CONFIG.backend_error;
	}

	if (error && error in AUTH_ERROR_CONFIG) return AUTH_ERROR_CONFIG[error as keyof typeof AUTH_ERROR_CONFIG];

	return AUTH_ERROR_CONFIG.keycloak_error;
};

const getRetryUrl = (redirect?: string) => {
	const loginUrl = "/auth/login";
	if (redirect && redirect !== "/") return `${loginUrl}?redirect=${encodeURIComponent(redirect)}`;
	return loginUrl;
};

export default async function AuthErrorPage({ searchParams }: IAuthErrorPageProps): Promise<ReactElement> {
	const { error, message, status, redirect } = await searchParams;
	const config = getErrorConfig(error, status);
	const IconComponent = config.icon;
	const displayMessage = message || config.defaultMessage;
	const retryUrl = getRetryUrl(redirect);

	return (
		<div
			className="from-primary/15 to-background error-page flex min-h-screen flex-col items-center justify-center bg-gradient-to-br px-4"
			role="alert"
			aria-live="assertive"
		>
			<div className="bg-background/95 rounded-main ring-primary/20 w-full max-w-xl border border-gray-100 p-8 shadow-2xl ring-2 backdrop-blur-xl">
				<div className="flex flex-col items-center text-center">
					<span
						className="from-primary/10 to-primary/10 animate-fade-in mb-6 inline-flex items-center justify-center rounded-full bg-gradient-to-br p-6 shadow-xl"
						aria-hidden="true"
					>
						<IconComponent className="text-primary h-14 w-14 drop-shadow-lg" />
					</span>
					<h1 className="from-primary to-primary animate-fade-in mb-2 bg-gradient-to-r bg-clip-text text-3xl font-extrabold tracking-tight text-transparent drop-shadow-xl">
						{config.title}
					</h1>
					<p className="animate-fade-in mb-6 max-w-lg text-base text-[var(--foreground)] delay-100">{displayMessage}</p>
					<div className="space-y-2">
						{status && (
							<p className="text-muted-foreground text-xs">
								Código do erro: <span className="font-mono font-semibold">{status}</span>
							</p>
						)}
						<p className="text-muted-foreground max-w-md text-xs">
							Se o problema persistir, entre em contato com o <span className="text-primary font-semibold">administrador do sistema</span>.
						</p>
					</div>
					{process.env.NODE_ENV === "development" && (
						<details className="mt-6 w-full max-w-md rounded-lg border border-gray-200 bg-gray-50 p-3 text-xs">
							<summary className="cursor-pointer font-semibold text-gray-700 transition-colors hover:text-gray-900">
								Detalhes técnicos (desenvolvimento)
							</summary>
							<div className="mt-2 space-y-1 text-gray-600">
								<p>
									<strong>Erro:</strong> {error || "N/A"}
								</p>
								<p>
									<strong>Status:</strong> {status || "N/A"}
								</p>
								<p>
									<strong>Redirect:</strong> {redirect || "N/A"}
								</p>
								<p>
									<strong>Mensagem:</strong> {message || "N/A"}
								</p>
							</div>
						</details>
					)}
					<Link href={retryUrl} className="mt-2">
						<Button variant="outline">Tentar Novamente</Button>
					</Link>
				</div>
			</div>
		</div>
	);
}
