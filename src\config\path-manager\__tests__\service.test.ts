import { AppAbility } from "@/config/permissions";
import { TPermissionAction } from "@/config/permissions/actions";
import { TPermissionSubject } from "@/config/permissions/subjects";
import { IUser } from "@/core/auth/types/user.types";
import { defineAbilitiesFor } from "@/shared/lib/permissions/ability";
import { pathItems } from "../items";
import { pathService } from "../service";

// Helper para construir habilidades
interface IPermissionTuple {
	action: TPermissionAction;
	subject: TPermissionSubject;
}
const buildAbility = (permissions: IPermissionTuple[]): AppAbility => {
	const user: IUser = { id: "u", name: "Test", email: "t@test", roles: [], permissions } as IUser;
	return defineAbilitiesFor(user);
};

describe("pathService.hasPermission", () => {
	test("libera quando não há requiredSubjects", () => {
		const ability = buildAbility([]);
		const item = { ...pathItems[0].items[0], requiredSubjects: [] };
		expect(pathService.hasPermission(item, ability)).toBe(true);
	});

	test("exige permissão explicita em 'all' quando somente ['all']", () => {
		const abilityWithoutAll = buildAbility([{ action: "read", subject: "activity" }]);
		const abilityWithAll = buildAbility([{ action: "read", subject: "all" }]);
		const abilityWithManageAll = buildAbility([{ action: "manage", subject: "all" }]);
		const item = {
			...pathItems.find(g => g.items.some(i => i.requiredSubjects?.[0] === "all"))!.items.find(i => i.requiredSubjects?.[0] === "all")!,
			requiredSubjects: ["all"] as TPermissionSubject[],
		};

		expect(pathService.hasPermission(item, abilityWithoutAll)).toBe(false);
		expect(pathService.hasPermission(item, abilityWithAll)).toBe(true);
		expect(pathService.hasPermission(item, abilityWithManageAll)).toBe(true);
	});

	test("qualquer subject listado concede se possuir read correspondente", () => {
		const ability = buildAbility([{ action: "read", subject: "activity" }]);
		const itemMulti = {
			...pathItems[3].items[0].subItems!.find(i => i.id === "activity")!,
			requiredSubjects: ["activity", "cells"] as TPermissionSubject[],
		};
		expect(pathService.hasPermission(itemMulti, ability)).toBe(true);
	});
});
