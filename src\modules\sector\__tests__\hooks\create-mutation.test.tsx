import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";
import { createPostRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { useCreateSectorMutation } from "../../hooks/create/mutation.hook";

jest.mock("@/shared/lib/requests", () => ({
	createPostRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canCreate: () => true })),
}));

const mockedCreatePostRequest = createPostRequest as jest.MockedFunction<typeof createPostRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Setor criado com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Erro ao criar setor" };

describe("useCreateSectorMutation", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});
	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve criar setor com sucesso", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useCreateSectorMutation(), { wrapper });
		await expect(result.current.createCell({ name: "Novo Setor" })).resolves.toEqual(mockSuccess);
	});

	it("deve lançar erro ao falhar", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useCreateSectorMutation(), { wrapper });
		await expect(result.current.createCell({ name: "" })).rejects.toThrow("Erro ao criar setor");
	});
});
