import { isAuthenticated<PERSON>tom } from "@/core/auth/atoms/auth.atom";
import { useAtomValue } from "jotai";
import { Tooltip, TooltipContent, TooltipTrigger } from "../../../../shared/components/shadcn/tooltip";
import { cn } from "../../../../shared/lib/shadcn/utils";
import { chatIsOpenAtom } from "../../atoms/controls/trigger.atom";
import { ChatTriggerButton } from "./button";

const sizeClasses = {
	sm: "h-12 w-12",
	md: "h-14 w-14",
	lg: "h-16 w-16",
};

const iconSizeClasses = {
	sm: "h-5 w-5",
	md: "h-6 w-6",
	lg: "h-7 w-7",
};

const positionClasses = {
	"bottom-right": "bottom-6 right-6",
	"bottom-left": "bottom-6 left-6",
	"top-right": "top-6 right-6",
	"top-left": "top-6 left-6",
};

interface ChatTriggerProps {
	position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
	size?: "sm" | "md" | "lg";
}

export const ChatTrigger = ({ position = "bottom-right", size = "md" }: ChatTriggerProps) => {
	const isOpen = useAtomValue(chatIsOpenAtom);
	const hasUserAccess = useAtomValue(isAuthenticatedAtom);
	if (!hasUserAccess) return null;

	return (
		<div className={cn("fixed z-50", positionClasses[position])}>
			<div
				className={cn(
					"absolute inset-0 rounded-full transition-all duration-300",
					sizeClasses[size],
					isOpen
						? "from-primary/20 via-primary/30 to-primary/20 animate-pulse bg-gradient-to-r"
						: "from-primary/10 via-primary/20 to-primary/10 bg-gradient-to-r",
					"opacity-60 blur-md",
				)}
			/>
			{!isOpen ? (
				<Tooltip>
					<TooltipTrigger asChild>
						<div>
							<ChatTriggerButton sizeClasses={sizeClasses} iconSizeClasses={iconSizeClasses} />
						</div>
					</TooltipTrigger>
					<TooltipContent
						side={position.includes("left") ? "right" : "left"}
						className="bg-slate-800 text-white dark:bg-slate-200 dark:text-slate-800"
					>
						Fale com a Doorinha
					</TooltipContent>
				</Tooltip>
			) : (
				<ChatTriggerButton sizeClasses={sizeClasses} iconSizeClasses={iconSizeClasses} />
			)}
		</div>
	);
};
