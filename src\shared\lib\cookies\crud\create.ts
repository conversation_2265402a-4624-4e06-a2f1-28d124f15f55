"use server";

import { DEFAULT_COOKIE_OPTIONS } from "@/config/cookies/options";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { cookies } from "next/headers";
import { ICreateCookieProps } from "../types";

export const createCookie = async (data: ICreateCookieProps): Promise<ApiResponse<IMessageGlobalReturn>> => {
	try {
		const cookiesOptions = { ...DEFAULT_COOKIE_OPTIONS, ...data.options };
		const cookieStore = await cookies();
		cookieStore.set(data.name, data.value, cookiesOptions);
		return {
			success: true,
			data: {
				message: `Cookie ${data.name} criado com sucesso`,
			},
			status: 201,
		};
	} catch (error) {
		return {
			success: false,
			data: {
				message: `Erro ao criar cookie: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
			},
			status: 500,
		};
	}
};
