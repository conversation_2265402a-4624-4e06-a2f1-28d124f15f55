import { TABS, useInspectionTabs } from "@/modules/inspection/hooks/tabs/inspection-tabs.hook";
import { act, renderHook } from "@testing-library/react";

const canMock = jest.fn();

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: () => ({
		can: (...args: unknown[]) => canMock(...args),
	}),
}));

const mockUseAtom = jest.fn();

jest.mock("jotai", () => {
	const actual = jest.requireActual("jotai");
	return {
		...actual,
		useAtom: (...args: unknown[]) => mockUseAtom(...args),
	};
});

beforeEach(() => {
	jest.clearAllMocks();
	mockUseAtom.mockReset();
	canMock.mockImplementation(() => true); // padrão: todas permissões concedidas
});

describe("useInspectionTabs", () => {
	it("retorna o activeTab inicial e availableTabs (todas permissões)", () => {
		const setActive = jest.fn();
		mockUseAtom.mockReturnValueOnce([TABS[0], setActive]);
		const { result } = renderHook(() => useInspectionTabs());
		expect(result.current.activeTab).toBe(TABS[0]);
		expect(result.current.availableTabs).toEqual(TABS);
		expect(result.current.isTabActive(TABS[0])).toBe(true);
		expect(result.current.isTabActive(TABS[1])).toBe(false);
	});

	it("ajusta para a primeira aba permitida quando primeira (default) não tem permissão", () => {
		// Nega apenas a primeira aba (medidas)
		canMock.mockImplementation((action: string, subject: string) => {
			if (subject === "inspection-measure") return false;
			return true;
		});
		const setActive = jest.fn();
		mockUseAtom.mockReturnValueOnce([TABS[0], setActive]);
		renderHook(() => useInspectionTabs());
		// Deve ter tentado alterar para 'campos'
		expect(setActive).toHaveBeenCalledWith("campos");
	});

	it("chama setActiveTab quando setActiveTab é usado", () => {
		const setActive = jest.fn();
		mockUseAtom.mockReturnValueOnce([TABS[1], setActive]);
		const { result } = renderHook(() => useInspectionTabs());
		act(() => result.current.setActiveTab(TABS[2]));
		expect(setActive).toHaveBeenCalledWith(TABS[2]);
	});

	it("goToNextTab avança corretamente com wrap-around", () => {
		const setActive = jest.fn();
		mockUseAtom.mockReturnValueOnce([TABS[TABS.length - 1], setActive]);
		const { result } = renderHook(() => useInspectionTabs());
		act(() => result.current.goToNextTab());
		expect(setActive).toHaveBeenCalledWith(TABS[0]);
	});

	it("goToPreviousTab retrocede corretamente com wrap-around", () => {
		const setActive = jest.fn();
		mockUseAtom.mockReturnValueOnce([TABS[0], setActive]);
		const { result } = renderHook(() => useInspectionTabs());
		act(() => result.current.goToPreviousTab());
		expect(setActive).toHaveBeenCalledWith(TABS[TABS.length - 1]);
	});
});
