"use client";

import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { cn } from "@/shared/lib/shadcn/utils";
import { AlertTriangle, RefreshCw, Search, XCircle, Database, WifiOff } from "lucide-react";
import { ReactElement, ReactNode } from "react";
import { AxiosError } from "axios";

type TTableErrorType =
  | "network"
  | "not-found"
  | "server-error"
  | "permission-denied"
  | "timeout"
  | "validation"
  | "generic";

interface ITableErrorProps {
  /** Tipo do erro para exibir a mensagem e ícone apropriados */
  type?: TTableErrorType;
  /** Mensagem customizada de erro (sobrescreve a mensagem padrão do tipo) */
  message?: string;
  /** Descrição adicional do erro */
  description?: string;
  /** Função para tentar novamente */
  onRetry?: () => void;
  /** Função de busca/filtro alternativa */
  onSearch?: () => void;
  /** Texto do botão de retry (padrão: "Tentar novamente") */
  retryLabel?: string;
  /** Texto do botão de busca (padrão: "Buscar novamente") */
  searchLabel?: string;
  /** Mostrar apenas o ícone sem texto */
  compact?: boolean;
  /** Classe CSS adicional */
  className?: string;
  /** Conteúdo customizado para substituir completamente o erro padrão */
  children?: ReactNode;
  /** Número de colunas da tabela para colspan */
  colSpan?: number;
  /** Altura mínima do container de erro */
  minHeight?: string;
  /** Error object para detalhes técnicos */
  error?: Error | AxiosError;
  /** Mostrar detalhes técnicos do erro */
  showDetails?: boolean;
}

const errorConfig: Record<TTableErrorType, {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  message: string;
  color: string;
  bgColor: string;
  borderColor: string;
}> = {
  "network": {
    icon: WifiOff,
    title: "Erro de Conexão",
    message: "Não foi possível conectar ao servidor. Verifique sua conexão com a internet.",
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200"
  },
  "not-found": {
    icon: Search,
    title: "Nenhum Resultado",
    message: "Nenhum dado foi encontrado para os filtros aplicados.",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  "server-error": {
    icon: Database,
    title: "Erro do Servidor",
    message: "Ocorreu um erro interno no servidor. Tente novamente em alguns instantes.",
    color: "text-red-600",
    bgColor: "bg-red-50",
    borderColor: "border-red-200"
  },
  "permission-denied": {
    icon: XCircle,
    title: "Acesso Negado",
    message: "Você não tem permissão para visualizar estes dados.",
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  },
  "timeout": {
    icon: AlertTriangle,
    title: "Tempo Esgotado",
    message: "A requisição demorou muito para responder. Tente novamente.",
    color: "text-yellow-600",
    bgColor: "bg-yellow-50",
    borderColor: "border-yellow-200"
  },
  "validation": {
    icon: AlertTriangle,
    title: "Erro de Validação",
    message: "Os dados fornecidos não são válidos. Verifique os filtros aplicados.",
    color: "text-amber-600",
    bgColor: "bg-amber-50",
    borderColor: "border-amber-200"
  },
  "generic": {
    icon: AlertTriangle,
    title: "Erro Inesperado",
    message: "Ocorreu um erro inesperado ao carregar os dados.",
    color: "text-gray-600",
    bgColor: "bg-gray-50",
    borderColor: "border-gray-200"
  }
};

/**
 * Determina o tipo de erro baseado no error object
 */
const getErrorType = (error?: Error | AxiosError): TTableErrorType => {
  if (!error) return "generic";

  if (error instanceof AxiosError) {
    const status = error.response?.status;
    switch (status) {
      case 404:
        return "not-found";
      case 403:
      case 401:
        return "permission-denied";
      case 500:
      case 502:
      case 503:
        return "server-error";
      case 408:
        return "timeout";
      default:
        if (!error.response) return "network";
        return "generic";
    }
  }

  if (error.message.toLowerCase().includes("network")) return "network";
  if (error.message.toLowerCase().includes("timeout")) return "timeout";

  return "generic";
};

/**
 * Componente wrapper para exibir erros em tabelas de forma intuitiva e consistente
 */
export const TableError = ({
  type,
  message,
  description,
  onRetry,
  onSearch,
  retryLabel = "Tentar novamente",
  searchLabel = "Buscar novamente",
  compact = false,
  className,
  children,
  colSpan = 1,
  minHeight = "200px",
  error,
  showDetails = false
}: ITableErrorProps): ReactElement => {

  // Determina o tipo do erro automaticamente se não fornecido
  const errorType = type || getErrorType(error);
  const config = errorConfig[errorType];

  // Usa mensagem customizada ou padrão do tipo
  const errorMessage = message || config.message;

  // Se children foi fornecido, renderiza conteúdo customizado
  if (children) {
    return (
      <tr>
        <td colSpan={colSpan} className={cn("p-0", className)}>
          <div className="flex items-center justify-center" style={{ minHeight }}>
            {children}
          </div>
        </td>
      </tr>
    );
  }

  const IconComponent = config.icon;

  if (compact) {
    return (
      <tr>
        <td colSpan={colSpan} className={cn("p-4 text-center", className)}>
          <div className="flex items-center justify-center gap-3">
            <IconComponent className={cn("w-5 h-5", config.color)} />
            <span className={cn("text-sm font-medium", config.color)}>
              {errorMessage}
            </span>
            {onRetry && (
              <Button
                onClick={onRetry}
                variant="outline"
                size="sm"
                className="ml-2"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            )}
          </div>
        </td>
      </tr>
    );
  }

  return (
    <tr>
      <td colSpan={colSpan} className={cn("p-0", className)}>
        <div
          className={cn(
            "flex flex-col items-center justify-center p-8 m-4 rounded-main border-2 border-dashed transition-all duration-200",
            config.bgColor,
            config.borderColor
          )}
          style={{ minHeight }}
          role="alert"
          aria-live="polite"
        >
          {/* Ícone e título */}
          <div className="flex flex-col items-center mb-4">
            <div className={cn(
              "p-4 rounded-full mb-3 shadow-lg",
              config.bgColor,
              "ring-4 ring-white"
            )}>
              <IconComponent className={cn("w-8 h-8", config.color)} />
            </div>
            <h3 className={cn("text-xl font-bold mb-2", config.color)}>
              {config.title}
            </h3>
            <p className="text-gray-600 text-center max-w-md leading-relaxed">
              {errorMessage}
            </p>
            {description && (
              <p className="text-gray-500 text-sm text-center max-w-md mt-2">
                {description}
              </p>
            )}
          </div>

          {/* Botões de ação */}
          <div className="flex flex-wrap gap-3 justify-center">
            {onRetry && (
              <Button
                onClick={onRetry}
                variant="default"
                className="shadow-lg hover:shadow-xl transition-shadow"
              >
                <RefreshCw className="w-4 h-4" />
                {retryLabel}
              </Button>
            )}
            {onSearch && (
              <Button
                onClick={onSearch}
                variant="outline"
                className="shadow-sm hover:shadow-md transition-shadow"
              >
                <Search className="w-4 h-4" />
                {searchLabel}
              </Button>
            )}
          </div>

          {/* Detalhes técnicos (opcional) */}
          {showDetails && error && (
            <details className="mt-6 w-full max-w-md">
              <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 transition-colors">
                Detalhes técnicos
              </summary>
              <div className="mt-2 p-3 bg-gray-100 rounded-md text-xs text-gray-600 font-mono max-h-32 overflow-y-auto">
                <pre className="whitespace-pre-wrap break-all">
                  {error.message || "Erro desconhecido"}
                  {error instanceof AxiosError && error.response?.data && (
                    <>
                      {"\n\nResposta do servidor:"}
                      {JSON.stringify(error.response.data, null, 2)}
                    </>
                  )}
                </pre>
              </div>
            </details>
          )}

          {/* Dica de ajuda */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-400 max-w-sm">
              Se o problema persistir, entre em contato com o{" "}
              <span className="font-semibold text-primary">
                administrador do sistema
              </span>
            </p>
          </div>
        </div>
      </td>
    </tr>
  );
};

/**
 * Hook para facilitar o uso do TableError em tabelas com React Query ou similar
 */
export const useTableError = () => {
  const getTableErrorProps = (
    error: Error | AxiosError | null,
    isLoading: boolean = false
  ): Partial<ITableErrorProps> | null => {
    if (isLoading || !error) return null;

    return {
      type: getErrorType(error),
      error,
      showDetails: process.env.NODE_ENV === "development"
    };
  };

  return { getTableErrorProps };
};

export default TableError;