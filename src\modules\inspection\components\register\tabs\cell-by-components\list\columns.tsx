import { ColumnDef } from "@tanstack/react-table";
import { ICellByComponentDto } from "../../../../../types/cell-components/dtos/find-all.dto";
import { CellComponentActions } from "./actions";

export const inspectionCellColumns: ColumnDef<ICellByComponentDto>[] = [
	{
		accessorKey: "cellName",
		header: () => <div className="pl-[10px] text-start font-semibold">Nome da Célula</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-[10px]">
				<span className="text-primary max-w-[200px] truncate text-start font-medium">{row.original.cellName}</span>
			</div>
		),
	},
	{
		accessorKey: "componentName",
		header: () => <div className="text-center font-semibold">Nome do Componente</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.componentName}</span>
			</div>
		),
	},
	{
		accessorKey: "delete",
		header: () => <div className="pr-[10px] text-end font-semibold">Ações</div>,
		cell: ({ row }) => <CellComponentActions cellId={String(row.original.id)} name={`${row.original.cellName} - ${row.original.componentName}`} />,
	},
];
