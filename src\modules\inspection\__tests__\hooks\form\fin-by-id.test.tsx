import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { useFormFindById } from "@/modules/inspection/hooks/form/list/find-by-id.hook";
import { IFormFindByIdDto } from "@/modules/inspection/types/forms/dtos/find-by-id.dto";
import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";

if (!globalThis.fetch) globalThis.fetch = jest.fn();

jest.mock("@/shared/lib/requests", () => ({
	createGetRequest: jest.fn(),
}));

jest.mock("@/shared/hooks/permissions/permissions.hook", () => ({
	usePermissions: jest.fn(() => ({ canRead: () => true })),
}));


const mockedCreateGetRequest = createGetRequest as jest.MockedFunction<typeof createGetRequest>;

const mockedFormById: IFormFindByIdDto = {
	id: 1,
	title: "Test Form",
	text: "This is a test form.",
	nomenclature: "TF-001",
	revision: "A",
	developer: {
		id: "dev-1",
		name: "Developer Name",
	},
	approver: {
		id: "app-1",
		name: "Approver Name",
	},
	fields: [
		{
			id: 1,
			nickname: "field1",
			required: true,
			fieldType: { id: InspectionFormTypeEnum.INTEGER, name: "INTEGER" },
			field: { id: InspectionFormTypeEnum.INTEGER, name: "Field1" },
			measure: { id: 1, name: "Meter", abbreviation: "m" },
			group: 1,
			sequence: 1,
			groupTitle: "Group 1",
			biFilter: false,
			options: [
				{ id: 1, sequence: 1, option: "Option 1" },
				{ id: 2, sequence: 2, option: "Option 2" },
			],
		},
	],
	canUpdate: true,
};

const mockSuccessResponse: ApiResponse<IFormFindByIdDto> = {
	data: mockedFormById,
	success: true,
	status: 200,
};

const mockEmptyResponse: ApiResponse<IFormFindByIdDto> = {
	data: { message: "Formulário não encontrado" },
	success: false,
	status: 404,
};

const mockErrorResponse: ApiResponse<IFormFindByIdDto> = {
	data: { message: "Erro ao buscar o formulário" },
	success: false,
	status: 500,
};

describe("useFormFindById", () => {
	let queryClient: QueryClient;

	beforeEach(() => {
		queryClient = new QueryClient({
			defaultOptions: {
				queries: {
					retry: false,
				},
			},
		});
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve buscar o formulário por ID com sucesso", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);
		const { result } = renderHook(() => useFormFindById("1", true), { wrapper });
		await waitFor(() => expect(result.current.data).toEqual(mockedFormById));
		expect(result.current.hasError).toBe(false);
	});

	it("deve lidar com formulário não encontrado (404)", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockEmptyResponse);
		const { result } = renderHook(() => useFormFindById("1", true), { wrapper });
		await waitFor(() => expect(result.current.isEmpty).toBe(true));
		expect(result.current.data).toEqual(null);
		expect(result.current.hasError).toBe(false);
		expect(result.current.error).toBe("Formulário não encontrado");
	});

	it("deve lidar com erro ao buscar o formulário (500)", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockErrorResponse);
		const { result } = renderHook(() => useFormFindById("1", true), { wrapper });
		await waitFor(() => expect(result.current.hasError).toBe(true));
		expect(result.current.data).toEqual(null);
		expect(result.current.error).toBe("Erro ao buscar o formulário");
	});
});
