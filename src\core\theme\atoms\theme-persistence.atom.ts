import { atom } from "jotai";
import { ThemeId } from "../types/theme.types";
import { themePersistenceService } from "../services/theme-persistence.service";
import { currentThemeAtom, applyThemeAtom } from "./theme.atom";

/**
 * Atoms para gerenciamento de persistência de temas
 * Integração entre Jotai e serviços de persistência
 */

// Atom para controlar se a hidratação foi concluída
export const themeHydratedAtom = atom<boolean>(false);

// Atom para controlar se está sincronizando
export const themeSyncingAtom = atom<boolean>(false);

// Atom para erros de persistência
export const themePersistenceErrorAtom = atom<string | null>(null);

// Atom para inicializar tema do servidor/cliente
export const initializeThemeAtom = atom(
	null,
	async (get, set) => {
		try {
			set(themeSyncingAtom, true);
			set(themePersistenceErrorAtom, null);

			// Migra dados antigos se necessário
			await themePersistenceService.migrateOldData();

			// Obtém tema com fallback inteligente
			const theme = await themePersistenceService.getThemeWithFallback();
			
			// Valida o tema
			if (!themePersistenceService.validateTheme(theme)) {
				throw new Error(`Tema inválido detectado: ${theme}`);
			}

			// Aplica o tema
			set(applyThemeAtom, theme);
			
			// Marca como hidratado
			set(themeHydratedAtom, true);
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : "Erro ao inicializar tema";
			set(themePersistenceErrorAtom, errorMessage);
			console.error("Erro na inicialização do tema:", error);
			
			// Em caso de erro, usa tema padrão
			set(applyThemeAtom, "light");
			set(themeHydratedAtom, true);
		} finally {
			set(themeSyncingAtom, false);
		}
	}
);

// Atom para persistir tema (salva em cookie e localStorage)
export const persistThemeAtom = atom(
	null,
	async (get, set, themeId: ThemeId) => {
		try {
			set(themeSyncingAtom, true);
			set(themePersistenceErrorAtom, null);

			// Valida o tema
			if (!themePersistenceService.validateTheme(themeId)) {
				throw new Error(`Tema inválido: ${themeId}`);
			}

			// Sincroniza entre cookie e localStorage
			await themePersistenceService.syncTheme(themeId);

			// Aplica o tema
			set(applyThemeAtom, themeId);
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : "Erro ao persistir tema";
			set(themePersistenceErrorAtom, errorMessage);
			console.error("Erro ao persistir tema:", error);
			throw error;
		} finally {
			set(themeSyncingAtom, false);
		}
	}
);

// Atom para carregar tema do servidor (SSR)
export const loadServerThemeAtom = atom(
	null,
	async (get, set) => {
		try {
			const serverTheme = await themePersistenceService.loadThemeFromServer();
			if (serverTheme && themePersistenceService.validateTheme(serverTheme)) {
				set(currentThemeAtom, serverTheme);
				return serverTheme;
			}
			return null;
		} catch (error) {
			console.error("Erro ao carregar tema do servidor:", error);
			return null;
		}
	}
);

// Atom para carregar tema do cliente (hidratação)
export const loadClientThemeAtom = atom(
	null,
	(get, set) => {
		try {
			const clientTheme = themePersistenceService.loadThemeFromClient();
			if (clientTheme && themePersistenceService.validateTheme(clientTheme)) {
				set(currentThemeAtom, clientTheme);
				return clientTheme;
			}
			return null;
		} catch (error) {
			console.error("Erro ao carregar tema do cliente:", error);
			return null;
		}
	}
);

// Atom para limpar persistência
export const clearThemePersistenceAtom = atom(
	null,
	async (get, set) => {
		try {
			set(themeSyncingAtom, true);
			set(themePersistenceErrorAtom, null);

			await themePersistenceService.clearAllStorage();
			
			// Volta para tema padrão
			set(applyThemeAtom, "light");
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : "Erro ao limpar persistência";
			set(themePersistenceErrorAtom, errorMessage);
			console.error("Erro ao limpar persistência:", error);
		} finally {
			set(themeSyncingAtom, false);
		}
	}
);

// Atom derivado para status de persistência
export const themePersistenceStatusAtom = atom((get) => ({
	isHydrated: get(themeHydratedAtom),
	isSyncing: get(themeSyncingAtom),
	error: get(themePersistenceErrorAtom),
	stats: typeof window !== "undefined" ? themePersistenceService.getUsageStats() : null,
}));

// Atom para detectar mudanças de tema e persistir automaticamente
export const autoSyncThemeAtom = atom(
	null,
	(get, set, enable: boolean) => {
		if (!enable || typeof window === "undefined") return;

		// Observa mudanças no tema atual
		const unsubscribe = set.sub(currentThemeAtom, () => {
			const currentTheme = get(currentThemeAtom);
			const isHydrated = get(themeHydratedAtom);
			
			// Só persiste se já foi hidratado (evita loops na inicialização)
			if (isHydrated && currentTheme) {
				themePersistenceService.saveThemeToClient(currentTheme);
			}
		});

		return unsubscribe;
	}
);

// Atom para sincronização manual
export const manualSyncThemeAtom = atom(
	null,
	async (get, set) => {
		const currentTheme = get(currentThemeAtom);
		if (currentTheme) {
			await set(persistThemeAtom, currentTheme);
		}
	}
);

// Atom para verificar se há conflito entre servidor e cliente
export const themeConflictAtom = atom(
	null,
	async (get, set) => {
		try {
			const serverTheme = await themePersistenceService.loadThemeFromServer();
			const clientTheme = themePersistenceService.loadThemeFromClient();
			
			if (serverTheme && clientTheme && serverTheme !== clientTheme) {
				console.warn(`Conflito de tema detectado: servidor=${serverTheme}, cliente=${clientTheme}`);
				
				// Prioriza o tema do cliente (mais recente)
				await themePersistenceService.syncTheme(clientTheme);
				set(applyThemeAtom, clientTheme);
				
				return {
					hasConflict: true,
					serverTheme,
					clientTheme,
					resolved: clientTheme,
				};
			}
			
			return {
				hasConflict: false,
				serverTheme,
				clientTheme,
				resolved: serverTheme || clientTheme,
			};
		} catch (error) {
			console.error("Erro ao verificar conflito de tema:", error);
			return {
				hasConflict: false,
				error: error instanceof Error ? error.message : "Erro desconhecido",
			};
		}
	}
);
