import { IChatError } from "../../types/handlers.type";

export class StreamErrorHandler {
	async throwHttp(response: Response): Promise<never> {
		let errorMessage = `Erro HTTP ${response.status}`;
		try {
			const errorText = await response.text();
			if (errorText) errorMessage += ` - ${errorText}`;
		} catch {
			errorMessage += " - Não foi possível ler a mensagem de erro";
		}
		throw new Error(errorMessage);
	}

	create(error: unknown, customMessage?: string): IChatError {
		return {
			message: customMessage || (error instanceof Error ? error.message : "Erro desconhecido no streaming"),
			details: error,
		};
	}
}
