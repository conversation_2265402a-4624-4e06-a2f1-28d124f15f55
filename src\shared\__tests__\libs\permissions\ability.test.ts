import { IUser } from "@/core/auth/types/user.types";
import { defineAbilitiesFor } from "@/shared/lib/permissions/ability";

describe("defineAbilitiesFor", () => {
	let consoleWarnSpy: jest.SpyInstance;

	beforeEach(() => {
		consoleWarnSpy = jest.spyOn(console, "warn").mockImplementation(() => {});
	});

	afterEach(() => {
		consoleWarnSpy.mockRestore();
	});

	test("retorna habilidade vazia quando o usuário é nulo", () => {
		const ability = defineAbilitiesFor(null);
		expect(ability.can("read", "Foo")).toBe(false);
		expect(ability.can("manage", "all")).toBe(false);
	});

	test("retorna habilidade vazia quando o usuário não tem permissões", () => {
		const user: IUser = {
			id: "u1",
			name: "User One",
			email: "<EMAIL>",
			roles: [],
			permissions: [],
		};
		const ability = defineAbilitiesFor(user);
		expect(ability.can("read", "Foo")).toBe(false);
	});

	test("manage:all concede todas as permissões", () => {
		const user: IUser = {
			id: "u2",
			name: "User Two",
			email: "<EMAIL>",
			roles: [],
			permissions: [{ action: "manage", subject: "all" }],
		};
		const ability = defineAbilitiesFor(user);
		expect(ability.can("read", "Foo")).toBe(true);
		expect(ability.can("create", "Bar")).toBe(true);
		expect(ability.can("manage", "all")).toBe(true);
	});

	test("manage em um assunto específico concede CRUD + manage apenas para esse assunto", () => {
		const user: IUser = {
			id: "u2",
			name: "User Two",
			email: "<EMAIL>",
			roles: [],
			permissions: [{ action: "manage", subject: "activity" }],
		};
		const ability = defineAbilitiesFor(user);
		expect(ability.can("create", "activity")).toBe(true);
		expect(ability.can("read", "activity")).toBe(true);
		expect(ability.can("update", "activity")).toBe(true);
		expect(ability.can("delete", "activity")).toBe(true);
		expect(ability.can("manage", "activity")).toBe(true);

		// Outros assuntos não devem ser permitidos
		expect(ability.can("read", "Bar")).toBe(false);
		expect(ability.can("create", "Bar")).toBe(false);
	});

	test("ação específica em assunto concede apenas essa ação", () => {
		const user: IUser = { id: "u2", name: "User Two", email: "<EMAIL>", roles: [], permissions: [{ action: "read", subject: "activity" }] };
		const ability = defineAbilitiesFor(user);
		expect(ability.can("read", "activity")).toBe(true);
		expect(ability.can("create", "activity")).toBe(false);
		expect(ability.can("manage", "activity")).toBe(false);
	});

	test("ação ou assunto inválido é ignorado e gera um aviso", () => {
		const user: IUser = {
			id: "u2",
			name: "User Two",
			email: "<EMAIL>",
			roles: [],
			permissions: [{ action: "update", subject: "activity" }],
		};

		const ability = defineAbilitiesFor(user);
		expect(ability.can("read", "activity")).toBe(false);
		expect(consoleWarnSpy).not.toHaveBeenCalled();
	});
});
