import { toast } from "@/core/toast";
import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export function useDeleteFieldMutation() {
	const queryClient = useQueryClient();
	const { canDelete } = usePermissions();

	const deleteFields = useMutation({
		mutationKey: inspectionKeys.fields.custom("delete"),
		mutationFn: async (id: string) => {
			if (!canDelete(INSPECTION_SUBJECTS.INSPECTION_FIELDS)) throw new Error("Você não tem permissão para deletar campos.");
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(FIELDS_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.fields.invalidateAll(queryClient),
	});

	return {
		deleteField: (id: string) =>
			toast.promise(deleteFields.mutateAsync(id), {
				loading: "Excluindo campo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
}
