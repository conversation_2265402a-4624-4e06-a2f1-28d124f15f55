import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Link, X } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { GenericSearchSelect } from "../../../../../../../shared/components/custom/generic-search-select";
import { But<PERSON> } from "../../../../../../../shared/components/shadcn/button";
import { useFindAllActivity } from "../../../../../../activity/hooks/list/find-all.hook";
import { useFindAllCell } from "../../../../../../cell/hooks/list/find-all.hook";
import { useFindAllInspectionCellByProductType } from "../../../../../hooks/cell-by-product-type/list/find-all.hook";
import { useFindAllCellByComponents } from "../../../../../hooks/cell-components/list/find-all.hook";
import { useFindAllForms } from "../../../../../hooks/form/list/find-all.hook";
import { TCreateFormLink } from "../../../../../validators/forms-links/create";
import { requiredLabel } from "../../forms/form-item/form";

interface ICreateFormLinkForm {
	methods: UseFormReturn<TCreateFormLink>;
	onSubmit: (data: TCreateFormLink) => void;
	onClose: () => void;
}

export const FormCreateFormLink = ({ methods, onSubmit, onClose }: ICreateFormLinkForm) => {
	return (
		<div className="mx-auto">
			<Form {...methods}>
				<form onSubmit={methods.handleSubmit(onSubmit, console.log)}>
					<div className="space-y-5">
						<FormField
							control={methods.control}
							name="activity"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="font-semibold">{requiredLabel("Atividade")}</FormLabel>
									<FormControl className="w-full">
										<GenericSearchSelect
											value={field?.value}
											useDataHook={useFindAllActivity}
											onChange={value => field.onChange(value)}
											displayField={item => item.name}
											placeholder="Selecione..."
											searchPlaceholder="Buscar atividade..."
											loadingText="Carregando..."
											emptyText="Nenhuma atividade encontrada."
											width="w-full"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={methods.control}
							name="form"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="font-semibold">{requiredLabel("Formulário")}</FormLabel>
									<FormControl className="w-full">
										<GenericSearchSelect
											value={field?.value}
											useDataHook={useFindAllForms}
											displayField={item => `${item.title} - ${item.nomenclature} - ${item.revision}`}
											onChange={value => field.onChange(value)}
											placeholder="Selecione..."
											searchPlaceholder="Buscar formulário..."
											loadingText="Carregando..."
											emptyText="Nenhum formulário encontrado."
											width="w-full"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={methods.control}
							name="cell"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="font-semibold">{requiredLabel("Célula")}</FormLabel>
									<FormControl className="w-full">
										<GenericSearchSelect
											value={field?.value}
											useDataHook={useFindAllCell}
											onChange={value => field.onChange(value)}
											displayField={item => item.name}
											placeholder="Selecione..."
											searchPlaceholder="Buscar célula..."
											loadingText="Carregando..."
											emptyText="Nenhuma célula encontrada."
											width="w-full"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={methods.control}
							name="cellByProductType"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="font-semibold">Célula por Tipo de Produto</FormLabel>
									<FormControl className="w-full">
										<GenericSearchSelect
											value={field.value ?? { id: "", name: "" }}
											useDataHook={useFindAllInspectionCellByProductType}
											onChange={value => field.onChange(value)}
											displayField={item => `${item.cellName ?? ""} - ${item.productTypeName ?? ""}`}
											placeholder="Selecione..."
											searchPlaceholder="Buscar célula por tipo de produto..."
											loadingText="Carregando..."
											emptyText="Nenhuma célula encontrada."
											width="w-full"
										/>
									</FormControl>
								</FormItem>
							)}
						/>
						<FormField
							control={methods.control}
							name="cellByComponent"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="font-semibold">Célula por Tipo de Componente</FormLabel>
									<FormControl className="w-full">
										<GenericSearchSelect
											value={field.value ?? { id: "", name: "" }}
											useDataHook={useFindAllCellByComponents}
											onChange={value => field.onChange(value)}
											placeholder="Selecione..."
											displayField={item => `${item.cellName ?? ""} - ${item.componentName ?? ""}`}
											searchPlaceholder="Buscar célula por tipo de componente..."
											loadingText="Carregando..."
											emptyText="Nenhuma célula encontrada."
											width="w-full"
										/>
									</FormControl>
								</FormItem>
							)}
						/>
					</div>

					<footer className="mt-8 flex justify-end gap-3 border-t pt-8">
						<Button type="button" variant="outline" onClick={onClose} className="px-6 py-2">
							<X className="mr-2" /> Cancelar
						</Button>
						<Button type="submit" className="bg-primary hover:bg-primary/30 px-6 py-2 text-white">
							<Link className="mr-2" />
							Vincular
						</Button>
					</footer>
				</form>
			</Form>
		</div>
	);
};
