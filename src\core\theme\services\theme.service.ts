import { getDefaultStore } from "jotai";
import { 
	IThemeService, 
	IThemeConfig, 
	ThemeId, 
	CustomThemeId,
	IThemeValidation,
	THEME_CONSTANTS 
} from "../types/theme.types";
import {
	currentThemeAtom,
	currentThemeConfigAtom,
	themeState<PERSON>tom,
	applyThemeAtom,
	addCustomThemeAtom,
	updateCustomThemeAtom,
	removeCustomThemeAtom,
	systemPreferenceAtom,
} from "../atoms/theme.atom";
import { 
	DEFAULT_THEMES, 
	CSS_VARIABLE_MAP,
	THEME_CSS_CLASSES 
} from "../constants/themes.constants";
import { SIMP_STORE } from "@/config/providers/global";

/**
 * Serviço principal para gerenciamento de temas
 * Implementa IThemeService seguindo princípios SOLID
 */
export class ThemeService implements IThemeService {
	private store = SIMP_STORE || getDefaultStore();

	/**
	 * Obtém o tema atual
	 */
	getCurrentTheme(): ThemeId {
		return this.store.get(currentThemeAtom);
	}

	/**
	 * Define um novo tema
	 */
	async setTheme(themeId: ThemeId): Promise<void> {
		try {
			// Valida se o tema existe
			const validation = this.validateTheme(themeId);
			if (!validation.isValid) {
				throw new Error(`Tema inválido: ${validation.errors.join(", ")}`);
			}

			// Aplica o tema
			this.store.set(applyThemeAtom, themeId);

			// Aguarda um frame para garantir que as mudanças foram aplicadas
			await new Promise(resolve => requestAnimationFrame(resolve));
		} catch (error) {
			throw new Error(`Erro ao definir tema: ${error instanceof Error ? error.message : "Erro desconhecido"}`);
		}
	}

	/**
	 * Obtém a configuração de um tema específico
	 */
	getThemeConfig(themeId: ThemeId): IThemeConfig | null {
		const state = this.store.get(themeStateAtom);
		
		if (themeId === "system") {
			const systemPreference = this.store.get(systemPreferenceAtom);
			return state.availableThemes.find(theme => theme.id === systemPreference) || null;
		}
		
		return state.availableThemes.find(theme => theme.id === themeId) || null;
	}

	/**
	 * Obtém todos os temas disponíveis
	 */
	getAllThemes(): IThemeConfig[] {
		const state = this.store.get(themeStateAtom);
		return state.availableThemes;
	}

	/**
	 * Cria um novo tema customizado
	 */
	async createCustomTheme(config: Omit<IThemeConfig, "id" | "isCustom">): Promise<IThemeConfig> {
		try {
			// Valida a configuração do tema
			const validation = this.validateThemeConfig(config);
			if (!validation.isValid) {
				throw new Error(`Configuração de tema inválida: ${validation.errors.join(", ")}`);
			}

			// Cria o tema
			const newTheme = this.store.set(addCustomThemeAtom, config);
			return newTheme;
		} catch (error) {
			throw new Error(`Erro ao criar tema customizado: ${error instanceof Error ? error.message : "Erro desconhecido"}`);
		}
	}

	/**
	 * Atualiza um tema customizado existente
	 */
	async updateCustomTheme(id: CustomThemeId, config: Partial<IThemeConfig>): Promise<IThemeConfig> {
		try {
			// Verifica se é um tema customizado
			const existingTheme = this.getThemeConfig(id);
			if (!existingTheme) {
				throw new Error(`Tema com ID "${id}" não encontrado`);
			}
			
			if (!existingTheme.isCustom) {
				throw new Error("Não é possível atualizar temas padrão do sistema");
			}

			// Valida as atualizações
			if (config.colors) {
				const validation = this.validateThemeColors(config.colors);
				if (!validation.isValid) {
					throw new Error(`Cores inválidas: ${validation.errors.join(", ")}`);
				}
			}

			// Atualiza o tema
			const updatedTheme = this.store.set(updateCustomThemeAtom, { id, updates: config });
			return updatedTheme;
		} catch (error) {
			throw new Error(`Erro ao atualizar tema customizado: ${error instanceof Error ? error.message : "Erro desconhecido"}`);
		}
	}

	/**
	 * Remove um tema customizado
	 */
	async deleteCustomTheme(id: CustomThemeId): Promise<void> {
		try {
			// Verifica se é um tema customizado
			const existingTheme = this.getThemeConfig(id);
			if (!existingTheme) {
				throw new Error(`Tema com ID "${id}" não encontrado`);
			}
			
			if (!existingTheme.isCustom) {
				throw new Error("Não é possível remover temas padrão do sistema");
			}

			// Remove o tema
			this.store.set(removeCustomThemeAtom, id);
		} catch (error) {
			throw new Error(`Erro ao remover tema customizado: ${error instanceof Error ? error.message : "Erro desconhecido"}`);
		}
	}

	/**
	 * Obtém a preferência do sistema
	 */
	getSystemPreference(): "light" | "dark" {
		return this.store.get(systemPreferenceAtom);
	}

	/**
	 * Aplica um tema diretamente ao DOM (para uso em SSR)
	 */
	applyThemeToDOM(themeId: ThemeId): void {
		if (typeof window === "undefined") return;

		try {
			const themeConfig = this.getThemeConfig(themeId);
			if (!themeConfig) {
				console.warn(`Tema "${themeId}" não encontrado, usando tema padrão`);
				return;
			}

			// Remove classes anteriores
			document.documentElement.classList.remove(...Object.values(THEME_CSS_CLASSES));

			// Aplica nova classe
			const cssClass = THEME_CSS_CLASSES[themeId as keyof typeof THEME_CSS_CLASSES];
			if (cssClass) {
				document.documentElement.classList.add(cssClass);
			}

			// Define data-theme
			document.documentElement.setAttribute("data-theme", themeId);

			// Aplica CSS variables
			const root = document.documentElement.style;
			Object.entries(themeConfig.colors).forEach(([key, value]) => {
				const cssVar = CSS_VARIABLE_MAP[key as keyof typeof CSS_VARIABLE_MAP];
				if (cssVar) {
					root.setProperty(cssVar, value);
				}
			});
		} catch (error) {
			console.error("Erro ao aplicar tema ao DOM:", error);
		}
	}

	/**
	 * Valida se um tema existe
	 */
	private validateTheme(themeId: ThemeId): IThemeValidation {
		const errors: string[] = [];

		if (!themeId) {
			errors.push("ID do tema é obrigatório");
		}

		if (themeId !== "system") {
			const themeConfig = this.getThemeConfig(themeId);
			if (!themeConfig) {
				errors.push(`Tema "${themeId}" não encontrado`);
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}

	/**
	 * Valida a configuração de um tema
	 */
	private validateThemeConfig(config: Partial<IThemeConfig>): IThemeValidation {
		const errors: string[] = [];

		if (!config.name || config.name.trim().length === 0) {
			errors.push("Nome do tema é obrigatório");
		}

		if (config.colors) {
			const colorValidation = this.validateThemeColors(config.colors);
			errors.push(...colorValidation.errors);
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}

	/**
	 * Valida as cores de um tema
	 */
	private validateThemeColors(colors: Partial<IThemeConfig["colors"]>): IThemeValidation {
		const errors: string[] = [];
		const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
		const oklchRegex = /^oklch\([^)]+\)$/;

		Object.entries(colors).forEach(([key, value]) => {
			if (typeof value !== "string") {
				errors.push(`Cor "${key}" deve ser uma string`);
				return;
			}

			// Valida formato de cor (hex ou oklch)
			if (!hexColorRegex.test(value) && !oklchRegex.test(value) && !value.startsWith("rgb") && !value.startsWith("hsl")) {
				errors.push(`Cor "${key}" tem formato inválido: ${value}`);
			}
		});

		return {
			isValid: errors.length === 0,
			errors,
		};
	}

	/**
	 * Detecta mudanças na preferência do sistema
	 */
	setupSystemPreferenceListener(): void {
		if (typeof window === "undefined") return;

		try {
			const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
			
			const handleChange = (e: MediaQueryListEvent) => {
				const newPreference = e.matches ? "dark" : "light";
				this.store.set(systemPreferenceAtom, newPreference);
				
				// Se o tema atual é "system", reaplica o tema
				const currentTheme = this.getCurrentTheme();
				if (currentTheme === "system") {
					this.applyThemeToDOM("system");
				}
			};

			// Adiciona listener
			mediaQuery.addEventListener("change", handleChange);

			// Retorna função de cleanup
			return () => mediaQuery.removeEventListener("change", handleChange);
		} catch (error) {
			console.warn("Não foi possível configurar listener de preferência do sistema:", error);
		}
	}
}

// Instância singleton do serviço
export const themeService = new ThemeService();
