"use client";

import { useModal } from "@/shared/hooks/utils/modal.hook";
import { useState } from "react";

export const useEditKnowledge = () => {
	const editModal = useModal();
	const [selectedId, setSelectedId] = useState<string | null>(null);

	const openEditModal = (id: string) => {
		setSelectedId(id);
		editModal.openModal();
	};

	const closeEditModal = () => {
		editModal.closeModal();
		setSelectedId(null);
	};

	return {
		isOpen: editModal.isOpen,
		selectedId,
		openEditModal,
		closeEditModal,
	};
};
