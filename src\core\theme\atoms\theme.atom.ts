import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { 
	IThemeState, 
	IThemeConfig, 
	ThemeId, 
	THEME_CONSTANTS 
} from "../types/theme.types";
import { 
	DEFAULT_THEMES, 
	LIGHT_THEME, 
	DARK_THEME,
	THEME_PERSISTENCE_CONFIG 
} from "../constants/themes.constants";

/**
 * Atoms para gerenciamento de estado do sistema de temas
 * Seguindo princípios SOLID e padrões do Jotai
 */

// Atom para detectar preferência do sistema
export const systemPreferenceAtom = atom<"light" | "dark">(() => {
	if (typeof window === "undefined") return "light";
	
	try {
		return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
	} catch {
		return "light";
	}
});

// Atom para tema atual com persistência
export const currentThemeAtom = atomWithStorage<ThemeId>(
	THEME_PERSISTENCE_CONFIG.localStorageKey,
	THEME_CONSTANTS.DEFAULT_THEME,
	undefined,
	{ getOnInit: true }
);

// Atom para temas disponíveis (padrão + customizados)
export const availableThemesAtom = atom<IThemeConfig[]>(DEFAULT_THEMES);

// Atom para temas customizados
export const customThemesAtom = atomWithStorage<IThemeConfig[]>(
	"simp-custom-themes",
	[],
	undefined,
	{ getOnInit: true }
);

// Atom para estado de carregamento
export const themeLoadingAtom = atom<boolean>(false);

// Atom para erros
export const themeErrorAtom = atom<string | null>(null);

// Atom derivado para configuração do tema atual
export const currentThemeConfigAtom = atom<IThemeConfig | null>((get) => {
	const currentTheme = get(currentThemeAtom);
	const availableThemes = get(availableThemesAtom);
	const customThemes = get(customThemesAtom);
	const systemPreference = get(systemPreferenceAtom);
	
	// Se for tema system, retorna a configuração baseada na preferência do sistema
	if (currentTheme === "system") {
		return systemPreference === "dark" ? DARK_THEME : LIGHT_THEME;
	}
	
	// Busca primeiro nos temas padrão
	const standardTheme = availableThemes.find(theme => theme.id === currentTheme);
	if (standardTheme) return standardTheme;
	
	// Busca nos temas customizados
	const customTheme = customThemes.find(theme => theme.id === currentTheme);
	if (customTheme) return customTheme;
	
	// Fallback para tema padrão
	return LIGHT_THEME;
});

// Atom derivado para o estado completo do tema
export const themeStateAtom = atom<IThemeState>((get) => ({
	currentTheme: get(currentThemeAtom),
	availableThemes: [...get(availableThemesAtom), ...get(customThemesAtom)],
	systemPreference: get(systemPreferenceAtom),
	isLoading: get(themeLoadingAtom),
	error: get(themeErrorAtom),
}));

// Atom para aplicar tema ao DOM
export const applyThemeAtom = atom(
	null,
	(get, set, themeId: ThemeId) => {
		try {
			set(themeLoadingAtom, true);
			set(themeErrorAtom, null);
			
			const availableThemes = get(availableThemesAtom);
			const customThemes = get(customThemesAtom);
			const systemPreference = get(systemPreferenceAtom);
			
			let themeConfig: IThemeConfig | null = null;
			
			// Resolve configuração do tema
			if (themeId === "system") {
				themeConfig = systemPreference === "dark" ? DARK_THEME : LIGHT_THEME;
			} else {
				themeConfig = [...availableThemes, ...customThemes].find(
					theme => theme.id === themeId
				) || LIGHT_THEME;
			}
			
			if (typeof window !== "undefined" && themeConfig) {
				// Remove classes de tema anteriores
				document.documentElement.classList.remove("theme-light", "theme-dark", "dark");
				
				// Aplica nova classe de tema
				if (themeId === "dark" || (themeId === "system" && systemPreference === "dark")) {
					document.documentElement.classList.add("theme-dark", "dark");
				} else {
					document.documentElement.classList.add("theme-light");
				}
				
				// Aplica data-theme attribute
				document.documentElement.setAttribute("data-theme", themeId);
				
				// Aplica CSS variables
				const root = document.documentElement.style;
				Object.entries(themeConfig.colors).forEach(([key, value]) => {
					const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
					root.setProperty(cssVar, value);
				});
			}
			
			set(currentThemeAtom, themeId);
		} catch (error) {
			set(themeErrorAtom, error instanceof Error ? error.message : "Erro ao aplicar tema");
		} finally {
			set(themeLoadingAtom, false);
		}
	}
);

// Atom para alternar entre light e dark
export const toggleThemeAtom = atom(
	null,
	(get, set) => {
		const currentTheme = get(currentThemeAtom);
		const systemPreference = get(systemPreferenceAtom);
		
		let newTheme: ThemeId;
		
		if (currentTheme === "system") {
			// Se está em system, vai para o oposto da preferência do sistema
			newTheme = systemPreference === "dark" ? "light" : "dark";
		} else if (currentTheme === "light") {
			newTheme = "dark";
		} else if (currentTheme === "dark") {
			newTheme = "light";
		} else {
			// Se é um tema customizado, alterna para light
			newTheme = "light";
		}
		
		set(applyThemeAtom, newTheme);
	}
);

// Atom para adicionar tema customizado
export const addCustomThemeAtom = atom(
	null,
	(get, set, themeConfig: Omit<IThemeConfig, "id" | "isCustom">) => {
		try {
			set(themeLoadingAtom, true);
			set(themeErrorAtom, null);
			
			const customThemes = get(customThemesAtom);
			const id = `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
			
			const newTheme: IThemeConfig = {
				...themeConfig,
				id,
				isCustom: true,
			};
			
			// Verifica se já existe um tema com o mesmo nome
			const existingTheme = customThemes.find(theme => theme.name === newTheme.name);
			if (existingTheme) {
				throw new Error(`Já existe um tema com o nome "${newTheme.name}"`);
			}
			
			set(customThemesAtom, [...customThemes, newTheme]);
			return newTheme;
		} catch (error) {
			set(themeErrorAtom, error instanceof Error ? error.message : "Erro ao criar tema customizado");
			throw error;
		} finally {
			set(themeLoadingAtom, false);
		}
	}
);

// Atom para atualizar tema customizado
export const updateCustomThemeAtom = atom(
	null,
	(get, set, { id, updates }: { id: string; updates: Partial<IThemeConfig> }) => {
		try {
			set(themeLoadingAtom, true);
			set(themeErrorAtom, null);
			
			const customThemes = get(customThemesAtom);
			const themeIndex = customThemes.findIndex(theme => theme.id === id);
			
			if (themeIndex === -1) {
				throw new Error(`Tema com ID "${id}" não encontrado`);
			}
			
			const updatedThemes = [...customThemes];
			updatedThemes[themeIndex] = { ...updatedThemes[themeIndex], ...updates };
			
			set(customThemesAtom, updatedThemes);
			
			// Se o tema sendo atualizado é o atual, reaplica
			const currentTheme = get(currentThemeAtom);
			if (currentTheme === id) {
				set(applyThemeAtom, id);
			}
			
			return updatedThemes[themeIndex];
		} catch (error) {
			set(themeErrorAtom, error instanceof Error ? error.message : "Erro ao atualizar tema customizado");
			throw error;
		} finally {
			set(themeLoadingAtom, false);
		}
	}
);

// Atom para remover tema customizado
export const removeCustomThemeAtom = atom(
	null,
	(get, set, id: string) => {
		try {
			set(themeLoadingAtom, true);
			set(themeErrorAtom, null);
			
			const customThemes = get(customThemesAtom);
			const currentTheme = get(currentThemeAtom);
			
			// Verifica se o tema existe
			const themeExists = customThemes.some(theme => theme.id === id);
			if (!themeExists) {
				throw new Error(`Tema com ID "${id}" não encontrado`);
			}
			
			// Remove o tema
			const updatedThemes = customThemes.filter(theme => theme.id !== id);
			set(customThemesAtom, updatedThemes);
			
			// Se o tema removido era o atual, volta para o padrão
			if (currentTheme === id) {
				set(applyThemeAtom, THEME_CONSTANTS.DEFAULT_THEME);
			}
		} catch (error) {
			set(themeErrorAtom, error instanceof Error ? error.message : "Erro ao remover tema customizado");
			throw error;
		} finally {
			set(themeLoadingAtom, false);
		}
	}
);

// Atom para limpar erros
export const clearThemeErrorAtom = atom(
	null,
	(get, set) => {
		set(themeErrorAtom, null);
	}
);
