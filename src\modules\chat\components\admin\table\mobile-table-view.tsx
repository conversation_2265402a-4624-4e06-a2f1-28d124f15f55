import { IChatKnowledgeDto } from "@/modules/chat/types/dtos/find-all-knowledge.dto";
import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";
import { Pagination } from "@/shared/components/custom/pagination";
import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { ComponentIcon } from "lucide-react";
import { ChatCardMobile } from "./card-mobile";

interface MobileTableViewProps {
	data: IChatKnowledgeDto[] | undefined;
	isLoading: boolean;
	hasError: boolean;
	error: string | false | undefined;
	searchTerm: string;
	pagination: {
		currentPage: number;
		totalPages: number;
		itemsPerPage: number;
		totalItems: number;
	} | null;
	onPageChange: (page: number) => void;
	onPageSizeChange: (size: number) => void;
}

export const MobileTableView = ({ data, isLoading, hasError, error, searchTerm, pagination, onPageChange, onPageSizeChange }: MobileTableViewProps) => {
	const renderContent = () => {
		if (hasError) {
			return <div className="h-24 text-center text-red-500">Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}</div>;
		}

		if (isLoading) return <Skeleton className="mx-auto h-6 w-48" />;
		if (data && data.length) return data.map(item => <ChatCardMobile key={item.id} item={item} />);

		return (
			<EmptyStateTable
				searchTerm={searchTerm}
				icon={<ComponentIcon />}
				title="Nenhum conhecimento encontrado"
				description={searchTerm ? "Nenhum conhecimento corresponde ao termo pesquisado." : "Ainda não há conhecimentos cadastrados."}
				tip="Você pode tentar pesquisar por outros termos ou adicionar um novo conhecimento."
			/>
		);
	};

	return (
		<div className="space-y-4">
			{renderContent()}
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					onPageChange={onPageChange}
					onPageSizeChange={size => {
						onPageSizeChange(size);
						onPageChange(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
