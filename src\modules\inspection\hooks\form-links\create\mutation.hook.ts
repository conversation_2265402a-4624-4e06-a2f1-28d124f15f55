import { INSPECTION_SUBJECTS } from "@/modules/inspection/constants/permissions/subjects";
import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../../core/toast";
import { createPostRequest } from "../../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { INSPECTION_FORMS_LINKS_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";
import { ICreateFormLinkDto } from "../../../types/form-link/dtos/create.dto";

export const useCreateFormLinkMutation = (onClose: () => void) => {
	const queryClient = useQueryClient();
	const { canCreate } = usePermissions();

	const createFormLinkMutation = useMutation({
		mutationKey: inspectionKeys.formsLink.custom("create"),
		mutationFn: async (form: ICreateFormLinkDto) => {
			if (!canCreate(INSPECTION_SUBJECTS.INSPECTION_FORM)) throw new Error("Você não tem permissão para criar este vínculo.");
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(INSPECTION_FORMS_LINKS_ENDPOINTS.CREATE, form);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => {
			inspectionKeys.formsLink.invalidateAll(queryClient);
			onClose();
		},
	});

	return {
		createFormLink: async (form: ICreateFormLinkDto) =>
			toast.promise(createFormLinkMutation.mutateAsync(form), {
				loading: "Criando vínculo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
