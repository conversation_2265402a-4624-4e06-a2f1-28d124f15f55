"use client";

import { usePermissions } from "@/shared/hooks/permissions/permissions.hook";
import { useAtom } from "jotai";
import { useCallback, useEffect, useRef } from "react";
import { activeTabsAtom } from "../../atoms/active-tabs.atom";
import { INSPECTION_SUBJECTS } from "../../constants/permissions/subjects";

export const TABS = ["medidas", "campos", "vinculo-colaboradores", "formularios", "tipo-produto", "componentes", "vinculos"] as const;
export type TInspectionTabValue = (typeof TABS)[number];

export interface IUseInspectionTabsReturn {
	activeTab: TInspectionTabValue;
	setActiveTab: (tab: TInspectionTabValue) => void;
	isTabActive: (tab: TInspectionTabValue) => boolean;
	goToNextTab: () => void;
	goToPreviousTab: () => void;
	availableTabs: readonly TInspectionTabValue[];
}

const TAB_PERMISSIONS: Record<TInspectionTabValue, (typeof INSPECTION_SUBJECTS)[keyof typeof INSPECTION_SUBJECTS]> = {
	medidas: INSPECTION_SUBJECTS.INSPECTION_MEASURE,
	campos: INSPECTION_SUBJECTS.INSPECTION_FIELDS,
	"vinculo-colaboradores": INSPECTION_SUBJECTS.INSPECTION_COLLABORATOR_BY_SECTOR,
	formularios: INSPECTION_SUBJECTS.INSPECTION_FORM,
	"tipo-produto": INSPECTION_SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_PRODUCT_TYPE,
	componentes: INSPECTION_SUBJECTS.INSPECTION_CELL_PRODUCTION_BY_COMPONENT,
	vinculos: INSPECTION_SUBJECTS.INSPECTION_FORM,
};

export const useInspectionTabs = (): IUseInspectionTabsReturn => {
	const [activeTab, setActiveTab] = useAtom(activeTabsAtom);
	const { can } = usePermissions();
	const hasAdjustedRef = useRef(false);

	useEffect(() => {
		if (hasAdjustedRef.current) return;
		const accessible = TABS.filter(tab => can("read", TAB_PERMISSIONS[tab]));
		if (accessible.length === 0) return;
		if (accessible.length === TABS.length) {
			hasAdjustedRef.current = true;
			return;
		}

		const first = accessible[0];
		if (activeTab !== first) {
			setActiveTab(first);
		}
		hasAdjustedRef.current = true;
	}, [can, setActiveTab, activeTab]);

	const isTabActive = useCallback((tab: TInspectionTabValue) => activeTab === tab, [activeTab]);

	const goToNextTab = useCallback(() => {
		const idx = TABS.indexOf(activeTab);
		setActiveTab(TABS[(idx + 1) % TABS.length]);
	}, [activeTab, setActiveTab]);

	const goToPreviousTab = useCallback(() => {
		const idx = TABS.indexOf(activeTab);
		setActiveTab(TABS[(idx - 1 + TABS.length) % TABS.length]);
	}, [activeTab, setActiveTab]);

	return {
		activeTab,
		setActiveTab,
		isTabActive,
		goToNextTab,
		goToPreviousTab,
		availableTabs: TABS,
	};
};
